'use client';

import { AnalysisStatus } from '@/hooks/useAnalysisStatus';
import { Workflow } from '@/types/database';
import { getAnalysisPeriodLabel, getResearchDepthLabel } from '@/utils/enums';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { AnalysisActionButton } from './AnalysisActionButton';

interface TaskTableProps {
  tasks: Workflow[];
  analysisStatuses: Record<string, AnalysisStatus>;
  onViewAnalysis?: (task: Workflow) => Promise<void>;
  onStartAnalysis?: (task: Workflow) => Promise<void>;
  onViewMessages?: (taskId: string) => Promise<void>;
  onViewDetails?: (task: Workflow) => void;
  startingTask?: string | null;
}

export function TaskTable({
  tasks,
  analysisStatuses,
  onViewAnalysis,
  onStartAnalysis,
  onViewMessages,
  onViewDetails,
  startingTask,
}: TaskTableProps) {
  // 获取任务状态样式
  const getTaskStatusStyle = (status: Workflow['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'running':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'cancelled':
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
      default:
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
    }
  };

  // 获取任务状态文本
  const getTaskStatusText = (status: Workflow['status']) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '运行中';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '待处理';
    }
  };

  // 获取分析状态文本
  const getAnalysisStatusText = (status?: string) => {
    switch (status) {
      case 'completed':
        return '分析完成';
      case 'running':
        return '分析中';
      case 'failed':
        return '分析失败';
      default:
        return '未开始';
    }
  };

  // 获取分析状态样式
  const getAnalysisStatusStyle = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'running':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
    }
  };

  if (tasks.length === 0) {
    return (
      <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700">
        <div className="text-center py-12">
          <p className="text-slate-500 dark:text-slate-400 mb-4">暂无任务数据</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
          <thead className="bg-slate-50 dark:bg-slate-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                股票代码
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                标题
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                任务状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                分析状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                研究深度
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                分析周期
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                创建时间
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
            {tasks.map((task) => {
              const analysisStatus = analysisStatuses[task.task_id];
              return (
                <tr
                  key={task.id}
                  className="hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-slate-900 dark:text-white">
                        {task.ticker}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-slate-900 dark:text-white max-w-xs truncate" title={task.title}>
                      {task.title}
                    </div>
                    {task.description && (
                      <div className="text-sm text-slate-500 dark:text-slate-400 max-w-xs truncate" title={task.description}>
                        {task.description}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTaskStatusStyle(
                        task.status
                      )}`}
                    >
                      {getTaskStatusText(task.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col space-y-1">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full w-fit ${getAnalysisStatusStyle(
                          analysisStatus?.status
                        )}`}
                      >
                        {getAnalysisStatusText(analysisStatus?.status)}
                      </span>
                      {analysisStatus?.status === 'running' && (
                        <div className="flex items-center space-x-2">
                          <div className="w-16 bg-gray-200 dark:bg-slate-600 rounded-full h-1">
                            <div
                              className="bg-blue-600 dark:bg-blue-500 h-1 rounded-full transition-all duration-300"
                              style={{ width: `${analysisStatus.progress}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-500 dark:text-slate-400">
                            {analysisStatus.progress}%
                          </span>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                    {task.research_depth ? getResearchDepthLabel(task.research_depth) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                    {task.analysis_period ? getAnalysisPeriodLabel(task.analysis_period) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                    {task.created_at
                      ? format(new Date(task.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })
                      : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => onViewDetails?.(task)}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                      >
                        详情
                      </button>
                      <button
                        onClick={() => onViewMessages?.(task.workflow_id)}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                      >
                        消息
                      </button>
                      <AnalysisActionButton
                        task={task}
                        analysisStatus={analysisStatus}
                        onStartAnalysis={onStartAnalysis}
                        onViewAnalysis={onViewAnalysis}
                        startingTask={startingTask}
                        size="sm"
                      />
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}