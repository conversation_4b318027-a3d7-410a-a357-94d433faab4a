#!/usr/bin/env python3
"""
AKShare 服务启动脚本
"""

import os
import sys
import uvicorn
import socket
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_port_available(port):
    """检查端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(('localhost', port))
        sock.close()
        return True
    except OSError:
        return False

def main():
    """启动 AKShare 服务"""
    print("🚀 启动 AKShare 数据服务...")
    print(f"📁 当前目录: {current_dir}")
    
    # 检查端口可用性
    port = 5000
    if not check_port_available(port):
        print(f"❌ 端口 {port} 已被占用")
        print("请检查是否有其他服务在使用此端口，或者终止现有服务")
        sys.exit(1)
    
    # 设置环境变量
    env_vars = {
        "DB_HOST": "************",
        "DB_PORT": "13306", 
        "DB_USER": "root",
        "DB_PASSWORD": "trading123",
        "DB_NAME": "trading_analysis"
    }
    
    print("🔧 设置环境变量:")
    for key, value in env_vars.items():
        os.environ.setdefault(key, value)
        # 不显示密码
        display_value = "***" if "PASSWORD" in key else value
        print(f"   {key}={display_value}")
    
    try:
        # 导入应用
        print("📦 导入应用模块...")
        from app.main import app
        
        print("✅ 应用导入成功")
        print(f"🌐 服务将启动在: http://0.0.0.0:{port}")
        print(f"📖 API 文档: http://localhost:{port}/docs")
        print(f"❤️  健康检查: http://localhost:{port}/health")
        print(f"🔍 根路径: http://localhost:{port}/")
        print()
        print("按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 启动服务
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=port,
            reload=False,
            log_level="info",
            access_log=True
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保:")
        print("1. 在 backend/akshare-service 目录下运行此脚本")
        print("2. 已安装所需依赖: pip install -r requirements.txt")
        print("3. app/main.py 文件存在且语法正确")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()