'use client';

import { AnalysisDropdown } from '@/components/navigation/AnalysisDropdown';
import { useNavigationHighlight } from '@/hooks/useNavigationHighlight';
import { authApi } from '@/lib/services/auth-service';
import useUserStore from '@/store/userStore';
import {
  Bars3Icon,
  ChartBarIcon,
  ChevronDownIcon,
  UserCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useLocale, useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ThemeToggle } from '../ui/ThemeToggle';

export function Header() {
  const t = useTranslations('navigation');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAnalysisDropdownOpen, setIsAnalysisDropdownOpen] = useState(false);
  const { user, loading, fetchUser, clearUser } = useUserStore();
  const { getNavigationItemClasses } = useNavigationHighlight();

  const handleLogout = async () => {
    try {
      await authApi.logout();
      // 清除用户状态和会话信息
      clearUser();
      router.push('/login');
      router.refresh();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleQuickStart = () => {
    if (user) {
      router.push(`/${locale}/create-task`);
    } else {
      router.push(`/${locale}/login`);
    }
  };

  const handleLanguageSwitch = () => {
    const newLocale = locale === 'zh' ? 'en' : 'zh';
    const currentPath = pathname?.replace(`/${locale}`, '');
    router.push(`/${newLocale}${currentPath}`);
  };

  const publicNavigation = [
    { name: t('features'), href: `/${locale}#features` },
    { name: t('examples'), href: `/${locale}#examples` },
    { name: t('docs'), href: `/${locale}#docs` },
  ];

  

  const handleAnalysisDropdownToggle = () => {
    setIsAnalysisDropdownOpen(!isAnalysisDropdownOpen);
  };

  const handleAnalysisDropdownClose = () => {
    setIsAnalysisDropdownOpen(false);
  };

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-slate-200 dark:border-slate-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-2">
            <Image src="/tradingAgentLogo.png" alt="TradingAgents" width={64} height={64} />
            <span className="text-xl font-bold text-slate-900 dark:text-white">TradingAgents</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8" role="menubar">
            {!user &&
              publicNavigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  role="menuitem"
                  className="text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors leading-[66px]"
                  aria-label={`导航到${item.name}`}
                >
                  {item.name}
                </a>
              ))}
         

            {/* Analysis Dropdown */}
            {user && (
              <div className="relative">
                <button
                  onClick={handleAnalysisDropdownToggle}
                  className={getNavigationItemClasses('/analysis', 'flex items-center space-x-1')}
                  aria-expanded={isAnalysisDropdownOpen}
                  aria-haspopup="menu"
                  aria-label="分析中心菜单"
                  aria-controls="analysis-dropdown"
                >
                  <ChartBarIcon className="h-5 w-5" aria-hidden="true" />
                  <span>分析中心</span>
                  <ChevronDownIcon
                    className={`h-4 w-4 transition-transform ${
                      isAnalysisDropdownOpen ? 'rotate-180' : ''
                    }`}
                    aria-hidden="true"
                  />
                </button>
                <AnalysisDropdown
                  isOpen={isAnalysisDropdownOpen}
                  onToggle={handleAnalysisDropdownToggle}
                  onClose={handleAnalysisDropdownClose}
                />
              </div>
            )}

            {user ? (
              <div className="flex items-center space-x-4">
                <span className="text-sm text-slate-600 dark:text-slate-300">
                  {t('welcome')}, {user.username}
                </span>

                {/* 语言切换按钮 */}
                {/* <button
                  onClick={handleLanguageSwitch}
                  className="flex items-center space-x-1 px-3 py-1 text-sm text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-md hover:bg-slate-100 dark:hover:bg-slate-700"
                >
                  <span className="text-base">{locale === 'zh' ? '🇺🇸' : '🇨🇳'}</span>
                  <span>{locale === 'zh' ? 'EN' : '中文'}</span>
                </button> */}

                <ThemeToggle />
                <div className="relative group">
                  <button className="flex items-center space-x-1 text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors">
                    <UserCircleIcon className="h-6 w-6" />
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-md shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <Link
                      href={`/${locale}/profile`}
                      className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700"
                    >
                      {t('profile')}
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700"
                    >
                      {t('logout')}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                {/* 语言切换按钮 */}
                <button
                  onClick={handleLanguageSwitch}
                  className="flex items-center space-x-1 px-3 py-1 text-sm text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-md hover:bg-slate-100 dark:hover:bg-slate-700"
                >
                  <span className="text-base">{locale === 'zh' ? '🇺🇸' : '🇨🇳'}</span>
                  <span>{locale === 'zh' ? 'EN' : '中文'}</span>
                </button>
                <ThemeToggle />
              </div>
            )}

            <button
              onClick={handleQuickStart}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              {user ? t('createTask') : t('quickStart')}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
              aria-label={isMobileMenuOpen ? '关闭移动菜单' : '打开移动菜单'}
            >
              {isMobileMenuOpen ? (
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div
            id="mobile-menu"
            className="md:hidden border-t border-slate-200 dark:border-slate-700 py-4"
            role="menu"
            aria-label="移动端导航菜单"
          >
            <div className="space-y-4">
              {publicNavigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}

              {/* Mobile Analysis Section */}
              {user && (
                <div className="border-t border-slate-200 dark:border-slate-700 pt-4">
                  <div className="mb-2">
                    <div className="flex items-center space-x-2 text-slate-900 dark:text-white font-medium">
                      <ChartBarIcon className="h-5 w-5" />
                      <span>分析中心</span>
                    </div>
                  </div>
                  <div className="ml-7 space-y-2">
                    <button
                      onClick={() => {
                        router.push('/analysis/history');
                        setIsMobileMenuOpen(false);
                      }}
                      className={getNavigationItemClasses(
                        '/analysis/history',
                        'block w-full text-left text-sm'
                      )}
                    >
                      分析历史
                    </button>
                    <button
                      onClick={() => {
                        router.push('/analysis/compare');
                        setIsMobileMenuOpen(false);
                      }}
                      className={getNavigationItemClasses(
                        '/analysis/compare',
                        'block w-full text-left text-sm'
                      )}
                    >
                      分析对比
                    </button>
                  </div>
                </div>
              )}

              {user ? (
                <>
                  <div className="border-t border-slate-200 dark:border-slate-700 pt-4">
                    <p className="text-sm text-slate-600 dark:text-slate-300 px-4">
                      欢迎, {user.username}
                    </p>
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      个人资料
                    </Link>
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsMobileMenuOpen(false);
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                    >
                      退出登录
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    登录
                  </Link>
                  <Link
                    href="/register"
                    className="block text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    注册
                  </Link>
                </>
              )}

              <button
                onClick={() => {
                  handleQuickStart();
                  setIsMobileMenuOpen(false);
                }}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-left"
              >
                {user ? '创建任务' : '快速开始'}
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
