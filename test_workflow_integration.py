#!/usr/bin/env python3
"""
测试 AKShare 工作流集成功能
验证前端到后端到数据库的完整数据流
"""

import requests
import json
import uuid
import time
import mysql.connector
from datetime import datetime

# 配置
BACKEND_URL = "http://localhost:5000"
DB_CONFIG = {
    'host': '************',
    'port': 13306,
    'user': 'root',
    'password': 'trading123',
    'database': 'trading_analysis'
}

def test_workflow_integration():
    """测试完整的工作流集成"""
    print("🚀 开始测试 AKShare 工作流集成功能\n")
    
    # 生成测试工作流ID
    test_workflow_id = f"test_wf_{uuid.uuid4().hex[:8]}"
    test_symbol = "000001"  # 平安银行
    
    print(f"📋 测试参数:")
    print(f"  工作流ID: {test_workflow_id}")
    print(f"  股票代码: {test_symbol}")
    print(f"  后端地址: {BACKEND_URL}")
    
    # 1. 测试后端健康检查
    print(f"\n1️⃣ 测试后端健康检查...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        return False
    
    # 2. 测试数据库连接
    print(f"\n2️⃣ 测试数据库连接...")
    try:
        db_conn = mysql.connector.connect(**DB_CONFIG)
        if db_conn.is_connected():
            print("✅ 数据库连接正常")
            db_conn.close()
        else:
            print("❌ 数据库连接失败")
            return False
    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")
        return False
    
    # 3. 测试带工作流ID的数据请求
    print(f"\n3️⃣ 测试带工作流ID的数据请求...")
    
    test_requests = [
        {
            "name": "股票历史数据",
            "endpoint": "/api/stock/history",
            "data": {
                "symbol": test_symbol,
                "period": "daily",
                "time_period": "1m",
                "workflow_id": test_workflow_id
            }
        },
        {
            "name": "股票新闻",
            "endpoint": "/api/stock/news", 
            "data": {
                "symbol": test_symbol,
                "limit": 5,
                "workflow_id": test_workflow_id
            }
        },
        {
            "name": "基本面数据",
            "endpoint": "/api/stock/fundamental",
            "data": {
                "symbol": test_symbol,
                "indicator": "all",
                "period_type": "按报告期",
                "workflow_id": test_workflow_id
            }
        }
    ]
    
    successful_requests = 0
    
    for req in test_requests:
        print(f"\n   📊 测试 {req['name']}...")
        try:
            response = requests.post(
                f"{BACKEND_URL}{req['endpoint']}", 
                json=req['data'], 
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ {req['name']} 请求成功")
                    print(f"      数据量: {result.get('count', 0)}")
                    print(f"      来源: {result.get('source', 'unknown')}")
                    successful_requests += 1
                else:
                    print(f"   ❌ {req['name']} 请求失败: {result.get('error', 'unknown')}")
            else:
                print(f"   ❌ {req['name']} HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {req['name']} 请求异常: {e}")
    
    print(f"\n   📈 数据请求结果: {successful_requests}/{len(test_requests)} 成功")
    
    # 4. 验证数据库记录
    print(f"\n4️⃣ 验证数据库记录...")
    
    # 等待一下让数据写入数据库
    time.sleep(2)
    
    try:
        db_conn = mysql.connector.connect(**DB_CONFIG)
        cursor = db_conn.cursor(dictionary=True)
        
        # 检查工作流事件记录
        cursor.execute(
            "SELECT COUNT(*) as event_count FROM workflow_events WHERE workflow_id = %s",
            (test_workflow_id,)
        )
        event_result = cursor.fetchone()
        event_count = event_result['event_count'] if event_result else 0
        
        # 检查数据获取日志记录
        cursor.execute(
            "SELECT COUNT(*) as log_count FROM data_fetch_logs WHERE workflow_id = %s",
            (test_workflow_id,)
        )
        log_result = cursor.fetchone()
        log_count = log_result['log_count'] if log_result else 0
        
        print(f"   📝 工作流事件记录: {event_count} 条")
        print(f"   📊 数据获取日志: {log_count} 条")
        
        if event_count > 0 and log_count > 0:
            print("   ✅ 数据库记录正常")
            
            # 显示详细记录
            print(f"\n   📋 详细记录:")
            cursor.execute(
                """SELECT data_type, symbol, data_count, execution_time_ms, status, created_at 
                   FROM data_fetch_logs 
                   WHERE workflow_id = %s 
                   ORDER BY created_at DESC""",
                (test_workflow_id,)
            )
            logs = cursor.fetchall()
            
            for log in logs:
                print(f"      - {log['data_type']}: {log['symbol']}, "
                      f"数据量: {log['data_count']}, "
                      f"耗时: {log['execution_time_ms']}ms, "
                      f"状态: {log['status']}")
                      
            database_success = True
        else:
            print("   ❌ 数据库记录缺失")
            database_success = False
            
        cursor.close()
        db_conn.close()
        
    except Exception as e:
        print(f"   ❌ 数据库验证失败: {e}")
        database_success = False
    
    # 5. 总结测试结果
    print(f"\n🎯 测试结果总结:")
    print(f"   后端服务: ✅")
    print(f"   数据库连接: ✅") 
    print(f"   数据请求: {'✅' if successful_requests == len(test_requests) else '❌'} ({successful_requests}/{len(test_requests)})")
    print(f"   数据库记录: {'✅' if database_success else '❌'}")
    
    overall_success = (
        successful_requests == len(test_requests) and 
        database_success
    )
    
    if overall_success:
        print(f"\n🎉 工作流集成测试完全成功！")
        print(f"📊 可以在数据库中查询工作流 {test_workflow_id} 的完整记录")
        print(f"SQL 查询示例:")
        print(f"  SELECT * FROM workflow_events WHERE workflow_id = '{test_workflow_id}';")
        print(f"  SELECT * FROM data_fetch_logs WHERE workflow_id = '{test_workflow_id}';")
    else:
        print(f"\n❌ 工作流集成测试部分失败，请检查配置")
    
    return overall_success

if __name__ == "__main__":
    success = test_workflow_integration()
    exit(0 if success else 1)