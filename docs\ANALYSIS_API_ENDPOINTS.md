# 分析 API 端点文档

本文档描述了为分析功能添加的所有 API 端点。

## 概述

我们为分析页面添加了完整的 API 支持，包括状态查询、停止分析、获取代理状态、分析报告和交易决策等功能。

## API 端点列表

### 1. 获取分析状态
- **路径**: `GET /api/analysis/{workflowId}/status`
- **描述**: 获取指定工作流的完整分析状态
- **响应**: 包含工作流状态、进度、代理报告、研究报告、最终决策等信息
- **实现**: `src/app/api/analysis/[workflowId]/status/route.ts`

### 2. 停止分析
- **路径**: `POST /api/analysis/{workflowId}/stop`
- **描述**: 停止正在运行的分析任务
- **响应**: 成功或失败消息
- **实现**: `src/app/api/analysis/[workflowId]/stop/route.ts`

### 3. 获取代理状态
- **路径**: `GET /api/analysis/{workflowId}/agents`
- **描述**: 获取分析中各个智能代理的状态信息
- **响应**: 包含分析师报告和研究员报告的状态
- **实现**: `src/app/api/analysis/[workflowId]/agents/route.ts`

### 4. 获取分析报告
- **路径**: `GET /api/analysis/{workflowId}/reports`
- **描述**: 获取所有分析报告，包括分析师报告、研究报告和最终决策
- **响应**: 完整的报告数据
- **实现**: `src/app/api/analysis/[workflowId]/reports/route.ts`

### 5. 获取交易决策
- **路径**: `GET /api/analysis/{workflowId}/decision`
- **描述**: 获取最终的交易决策结果
- **响应**: 交易决策数据或404（如果尚未生成）
- **实现**: `src/app/api/analysis/[workflowId]/decision/route.ts`

## API 客户端更新

### 新增 analysisApi 对象
在 `src/lib/api.ts` 中添加了新的 `analysisApi` 对象，包含以下方法：

```typescript
export const analysisApi = {
  // 获取分析状态
  getAnalysisStatus: (workflowId: string): Promise<ApiResponse<any>>
  
  // 停止分析
  stopAnalysis: (workflowId: string): Promise<ApiResponse<void>>
  
  // 获取代理状态
  getAgentStatuses: (workflowId: string): Promise<ApiResponse<any>>
  
  // 获取分析报告
  getReports: (workflowId: string): Promise<ApiResponse<any>>
  
  // 获取交易决策
  getTradingDecision: (workflowId: string): Promise<ApiResponse<any>>
  
  // 其他方法...
}
```

### 向后兼容性
原有的 `tradingApi` 对象保持不变，但内部调用已更新为使用新的 API 端点。

## 前端组件更新

### AnalysisOverview 组件
- 添加了停止分析按钮
- 支持显示停止分析的加载状态
- 只在分析运行时显示停止按钮

### 分析页面
- 添加了 `handleStopAnalysis` 处理函数
- 集成了停止分析功能
- 改进了错误处理和状态管理

## 错误处理

所有 API 端点都包含完整的错误处理：

1. **参数验证**: 检查必需的参数（如 workflowId）
2. **业务逻辑错误**: 处理特定的业务错误（如分析不存在）
3. **统一响应格式**: 所有响应都遵循统一的格式
4. **HTTP 状态码**: 正确的 HTTP 状态码（200, 400, 404, 500）

## 响应格式

所有 API 端点都返回统一的响应格式：

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}
```

## 依赖关系

这些 API 端点依赖于以下服务：

1. **AnalysisService**: 核心分析服务，处理业务逻辑
2. **EnhancedLangGraphDatabase**: 数据库操作层
3. **LangGraphService**: LangGraph 集成服务

## 使用示例

### 前端调用示例

```typescript
// 获取分析状态
const statusResponse = await analysisApi.getAnalysisStatus(workflowId);
if (statusResponse.success) {
  setAnalysisStatus(statusResponse.data);
}

// 停止分析
const stopResponse = await analysisApi.stopAnalysis(workflowId);
if (stopResponse.success) {
  console.log('分析已停止');
}
```

### 直接 HTTP 调用示例

```javascript
// 获取分析状态
const response = await fetch(`/api/analysis/${workflowId}/status`);
const result = await response.json();

// 停止分析
const stopResponse = await fetch(`/api/analysis/${workflowId}/stop`, {
  method: 'POST'
});
```

## 测试

建议为每个 API 端点编写单元测试和集成测试，确保：

1. 正确的参数验证
2. 业务逻辑正确执行
3. 错误情况正确处理
4. 响应格式符合规范

## 安全考虑

1. **身份验证**: 所有端点都应该包含适当的身份验证检查
2. **授权**: 确保用户只能访问自己的分析数据
3. **输入验证**: 严格验证所有输入参数
4. **速率限制**: 考虑添加速率限制以防止滥用

## 性能优化

1. **缓存**: 考虑为频繁访问的数据添加缓存
2. **分页**: 对于大量数据的端点，实现分页功能
3. **压缩**: 启用 HTTP 压缩以减少传输大小
4. **数据库优化**: 优化数据库查询性能

## 监控和日志

1. **请求日志**: 记录所有 API 请求和响应
2. **错误监控**: 监控和报告 API 错误
3. **性能指标**: 跟踪 API 响应时间和吞吐量
4. **业务指标**: 监控分析成功率和用户行为

## 未来改进

1. **WebSocket 集成**: 考虑使用 WebSocket 进行实时状态更新
2. **批量操作**: 支持批量获取多个分析的状态
3. **高级过滤**: 添加更多的查询和过滤选项
4. **API 版本控制**: 实现 API 版本控制以支持向后兼容性