import LangGraphDatabase from '@/lib/langgraph-database';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { ticker, title, description, config, created_by } = body;

    if (!ticker || !title || !created_by) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // The request body should align with the CreateWorkflowRequest type
    const workflowId = await LangGraphDatabase.createWorkflow({
      ticker,
      title,
      description,
      config: config || {},
      created_by: created_by.toString(),
    });

    return NextResponse.json({ success: true, data: { workflowId } }, { status: 200 });
  } catch (error) {
    console.error('Failed to create task:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { success: false, message: 'Failed to create task', error: errorMessage },
      { status: 500 }
    );
  }
}
