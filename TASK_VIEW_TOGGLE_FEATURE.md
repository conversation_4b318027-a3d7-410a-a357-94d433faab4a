# 任务视图切换功能

## 功能概述

为任务管理页面添加了卡片视图和表格视图的切换功能，用户可以根据自己的偏好选择不同的展示方式。

## 新增组件

### 1. ViewToggle 组件 (`src/components/tasks/ViewToggle.tsx`)

视图切换控制组件，提供卡片和表格两种视图模式的切换。

**特性：**
- 直观的图标和文字标识
- 响应式设计，支持暗色主题
- 无障碍访问支持
- 平滑的过渡动画

### 2. TaskTable 组件 (`src/components/tasks/TaskTable.tsx`)

表格视图组件，以表格形式展示任务列表。

**特性：**
- 紧凑的信息展示
- 支持进度条显示
- 响应式表格设计
- 完整的操作按钮集成
- 悬停效果和状态指示

## 功能特点

### 视图切换
- **卡片视图**：原有的网格卡片布局，适合详细浏览
- **表格视图**：紧凑的表格布局，适合快速扫描和批量操作

### 用户偏好持久化
- 用户的视图选择会自动保存到本地存储
- 下次访问时会自动恢复上次选择的视图模式

### 响应式设计
- 两种视图都支持响应式布局
- 在移动设备上自动适配

## 使用方法

1. 访问任务管理页面 (`/tasks`)
2. 在页面顶部找到视图切换按钮
3. 点击"卡片"或"表格"按钮切换视图
4. 选择会自动保存，下次访问时保持

## 技术实现

### 状态管理
```typescript
const [currentView, setCurrentView] = useState<ViewType>('grid');

const handleViewChange = (view: ViewType) => {
  setCurrentView(view);
  localStorage.setItem('tasks-view-preference', view);
};
```

### 视图渲染逻辑
```typescript
{currentView === 'grid' ? (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {/* 卡片视图 */}
  </div>
) : (
  <TaskTable
    tasks={tasks}
    analysisStatuses={taskAnalysisStatuses}
    // ... 其他属性
  />
)}
```

### 组件增强
- 为 `AnalysisActionButton` 添加了 `size` 属性支持
- 表格视图中使用小尺寸按钮以节省空间

## 界面截图

### 卡片视图
- 网格布局，每个任务占用一个卡片
- 详细的信息展示和进度指示
- 适合详细查看任务状态

### 表格视图
- 紧凑的表格布局
- 一行显示一个任务的关键信息
- 适合快速浏览和批量操作

## 未来改进

1. **排序功能**：为表格视图添加列排序功能
2. **筛选功能**：添加状态筛选和搜索功能
3. **批量操作**：在表格视图中支持批量选择和操作
4. **列自定义**：允许用户自定义表格显示的列
5. **导出功能**：支持将任务列表导出为 CSV 或 Excel

## 兼容性

- 支持所有现代浏览器
- 完全兼容现有的任务管理功能
- 不影响现有的 API 调用和数据流