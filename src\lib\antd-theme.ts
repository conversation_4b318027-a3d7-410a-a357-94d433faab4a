/**
 * Ant Design 主题配置
 * 根据项目的设计系统和深色模式配置 antd 组件主题
 */

import type { ThemeConfig } from 'antd';

// 项目主色调配置
export const THEME_COLORS = {
  primary: '#0ea5e9',      // sky-500
  primaryHover: '#0284c7', // sky-600
  success: '#22c55e',      // green-500
  warning: '#f59e0b',      // amber-500
  error: '#ef4444',        // red-500
  info: '#06b6d4',         // cyan-500
} as const;

// 深色模式颜色配置
export const DARK_COLORS = {
  bgBase: '#0f172a',       // slate-900
  bgContainer: '#1e293b',  // slate-800
  bgElevated: '#334155',   // slate-700
  border: '#475569',       // slate-600
  borderSecondary: '#64748b', // slate-500
  text: '#f8fafc',         // slate-50
  textSecondary: '#cbd5e1', // slate-300
  textTertiary: '#94a3b8', // slate-400
} as const;

// 浅色模式颜色配置
export const LIGHT_COLORS = {
  bgBase: '#ffffff',
  bgContainer: '#ffffff',
  bgElevated: '#f8fafc',   // slate-50
  border: '#e2e8f0',       // slate-200
  borderSecondary: '#cbd5e1', // slate-300
  text: '#1e293b',         // slate-800
  textSecondary: '#475569', // slate-600
  textTertiary: '#64748b', // slate-500
} as const;

/**
 * 获取 antd 主题配置
 * @param isDark 是否为深色模式
 * @returns antd ThemeConfig
 */
export function getAntdTheme(isDark: boolean): ThemeConfig {
  const colors = isDark ? DARK_COLORS : LIGHT_COLORS;
  
  return {
    token: {
      // 主色调
      colorPrimary: THEME_COLORS.primary,
      colorSuccess: THEME_COLORS.success,
      colorWarning: THEME_COLORS.warning,
      colorError: THEME_COLORS.error,
      colorInfo: THEME_COLORS.info,
      
      // 基础颜色
      colorBgBase: colors.bgBase,
      colorBgContainer: colors.bgContainer,
      colorBgElevated: colors.bgElevated,
      colorBorder: colors.border,
      colorBorderSecondary: colors.borderSecondary,
      colorText: colors.text,
      colorTextSecondary: colors.textSecondary,
      colorTextTertiary: colors.textTertiary,
      
      // 字体
      fontFamily: 'Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
      fontSize: 14,
      
      // 圆角
      borderRadius: 8,
      borderRadiusLG: 12,
      borderRadiusSM: 6,
      
      // 阴影
      boxShadow: isDark 
        ? '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)'
        : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      boxShadowSecondary: isDark
        ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
        : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    },
    components: {
      // Switch 组件
      Switch: {
        colorPrimary: THEME_COLORS.primary,
        colorPrimaryHover: THEME_COLORS.primaryHover,
        colorPrimaryBorder: THEME_COLORS.primary,
        trackHeight: 22,
        trackMinWidth: 44,
        handleSize: 18,
        handleSizeSM: 14,
      },
      
      // Card 组件
      Card: {
        colorBgContainer: colors.bgContainer,
        colorBorderSecondary: colors.borderSecondary,
        paddingLG: 24,
        headerBg: colors.bgContainer,
        headerHeight: 56,
      },
      
      // Input 组件
      Input: {
        colorBgContainer: colors.bgContainer,
        colorBorder: colors.border,
        colorText: colors.text,
        colorTextPlaceholder: colors.textTertiary,
        paddingBlock: 8,
        paddingInline: 12,
      },
      
      // Select 组件
      Select: {
        colorBgContainer: colors.bgContainer,
        colorBorder: colors.border,
        colorText: colors.text,
        colorTextPlaceholder: colors.textTertiary,
        optionSelectedBg: isDark ? '#1e40af20' : '#dbeafe',
        optionActiveBg: isDark ? '#1e40af10' : '#f0f9ff',
      },
      
      // Button 组件
      Button: {
        colorPrimary: THEME_COLORS.primary,
        colorPrimaryHover: THEME_COLORS.primaryHover,
        colorPrimaryActive: '#075985', // sky-800
        borderRadius: 8,
        paddingBlock: 8,
        paddingInline: 16,
        fontWeight: 500,
      },
      
      // Form 组件
      Form: {
        labelColor: colors.text,
        labelFontSize: 14,
        labelHeight: 32,
        itemMarginBottom: 24,
      },
      
      // Message 组件
      Message: {
        colorBgElevated: colors.bgElevated,
        colorText: colors.text,
        borderRadius: 8,
      },
      
      // Modal 组件
      Modal: {
        colorBgElevated: colors.bgElevated,
        colorText: colors.text,
        headerBg: colors.bgElevated,
        contentBg: colors.bgElevated,
        borderRadius: 12,
      },
      
      // Divider 组件
      Divider: {
        colorSplit: colors.borderSecondary,
        marginLG: 24,
      },
      
      // Upload 组件
      Upload: {
        colorBgContainer: colors.bgContainer,
        colorBorder: colors.border,
        colorText: colors.text,
      },
      
      // Avatar 组件
      Avatar: {
        colorBgContainer: colors.bgElevated,
        colorText: colors.text,
        borderRadius: 8,
      },
    },
  };
}

/**
 * 获取主题色调的 CSS 变量
 * @param isDark 是否为深色模式
 * @returns CSS 变量对象
 */
export function getThemeCSSVars(isDark: boolean) {
  const colors = isDark ? DARK_COLORS : LIGHT_COLORS;
  
  return {
    '--antd-primary-color': THEME_COLORS.primary,
    '--antd-success-color': THEME_COLORS.success,
    '--antd-warning-color': THEME_COLORS.warning,
    '--antd-error-color': THEME_COLORS.error,
    '--antd-info-color': THEME_COLORS.info,
    '--antd-bg-base': colors.bgBase,
    '--antd-bg-container': colors.bgContainer,
    '--antd-bg-elevated': colors.bgElevated,
    '--antd-border-color': colors.border,
    '--antd-text-color': colors.text,
    '--antd-text-secondary': colors.textSecondary,
  };
}
