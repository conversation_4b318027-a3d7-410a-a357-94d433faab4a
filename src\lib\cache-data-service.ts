/**
 * 缓存数据服务工具类
 * 提供从数据库缓存中读取基本面数据和新闻数据的接口
 */

import { serverApi } from './server-api';

// 缓存数据类型定义
export interface CachedFundamentalData {
  id: number;
  symbol: string;
  indicator: string;
  period_type: string;
  time_period: string;
  data: any;
  created_at: string;
  updated_at: string;
}

export interface CachedNewsData {
  id: number;
  news_id: string;
  source: string;
  title: string;
  content: string;
  publish_time: string;
  url: string;
  related_tickers: string[];
  created_at: string;
  updated_at: string;
}

// API响应类型
export interface CacheApiResponse<T> {
  success: boolean;
  data?: T;
  count?: number;
  message?: string;
}

// 请求参数类型
export interface CachedFundamentalDataParams {
  symbol: string;
  indicator?: string;
  period_type?: string;
  time_period?: string;
  limit?: number;
}

export interface CachedNewsDataParams {
  symbol: string;
  limit?: number;
}

/**
 * 缓存数据服务类
 */
export class CacheDataService {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || '/api/cache';
  }

  /**
   * 获取缓存的基本面数据
   */
  async getCachedFundamentalData(
    params: CachedFundamentalDataParams
  ): Promise<CacheApiResponse<CachedFundamentalData[]>> {
    try {
      const { symbol, indicator, period_type, time_period, limit } = params;

      const queryParams = new URLSearchParams();
      queryParams.append('symbol', symbol);
      if (indicator) queryParams.append('indicator', indicator);
      if (period_type) queryParams.append('period_type', period_type);
      if (time_period) queryParams.append('time_period', time_period);
      if (limit) queryParams.append('limit', limit.toString());

      const url = `${this.baseUrl}/fundamental?${queryParams.toString()}`;
      const response = await serverApi.get(url);

      return response.data;
    } catch (error) {
      console.error('获取缓存基本面数据失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '获取缓存基本面数据失败'
      };
    }
  }

  /**
   * 获取缓存的新闻数据
   */
  async getCachedNewsData(
    params: CachedNewsDataParams
  ): Promise<CacheApiResponse<CachedNewsData[]>> {
    try {
      const { symbol, limit } = params;

      const queryParams = new URLSearchParams();
      queryParams.append('symbol', symbol);
      if (limit) queryParams.append('limit', limit.toString());

      const url = `${this.baseUrl}/news?${queryParams.toString()}`;
      const response = await serverApi.get(url);

      return response.data;
    } catch (error) {
      console.error('获取缓存新闻数据失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '获取缓存新闻数据失败'
      };
    }
  }

  /**
   * 检查缓存数据是否存在
   */
  async checkCacheExists(
    dataType: 'fundamental' | 'news',
    params: { symbol: string; [key: string]: any }
  ): Promise<boolean> {
    try {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        queryParams.append(key, value.toString());
      });

      const url = `${this.baseUrl}/${dataType}/exists?${queryParams.toString()}`;
      const response = await serverApi.get(url);

      return response.data?.exists || false;
    } catch (error) {
      console.error('检查缓存数据存在性失败:', error);
      return false;
    }
  }

  /**
   * 清理过期的缓存数据
   */
  async cleanupExpiredCache(daysOld: number = 1): Promise<CacheApiResponse<{ deleted_records: number }>> {
    try {
      const response = await serverApi.post(`${this.baseUrl}/cleanup`, { days_old: daysOld });
      return response.data;
    } catch (error) {
      console.error('清理过期缓存数据失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '清理过期缓存数据失败'
      };
    }
  }
}

// 创建默认实例
export const cacheDataService = new CacheDataService();

// 导出工具函数
export const cacheUtils = {
  /**
   * 格式化缓存数据的时间显示
   */
  formatCacheTime: (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else {
      return `${diffDays}天前`;
    }
  },

  /**
   * 判断缓存是否过期
   */
  isCacheExpired: (timestamp: string, maxAgeHours: number = 24): boolean => {
    const cacheTime = new Date(timestamp);
    const now = new Date();
    const diffHours = (now.getTime() - cacheTime.getTime()) / 3600000;
    return diffHours > maxAgeHours;
  },

  /**
   * 从缓存数据中提取关键信息
   */
  extractFundamentalKeyInfo: (data: any): Record<string, any> => {
    if (!data || typeof data !== 'object') {
      return {};
    }

    // 提取关键的财务指标
    const keyInfo: Record<string, any> = {};
    
    // 遍历数据对象，提取数值型字段
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
        keyInfo[key] = typeof value === 'string' ? Number(value) : value;
      }
    });

    return keyInfo;
  }
};