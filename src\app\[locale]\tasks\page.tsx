'use client';

import { AnalysisPreview } from '@/components/tasks/AnalysisPreview';
import { TaskCard } from '@/components/tasks/TaskCard';
import { TaskTable } from '@/components/tasks/TaskTable';
import { TaskExecutionHistory } from '@/components/tasks/TaskExecutionHistory';
import { ViewToggle, ViewType } from '@/components/tasks/ViewToggle';
import { WorkflowInfo } from '@/components/tasks/WorkflowInfo';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useAnalysisStatus } from '@/hooks/useAnalysisStatus';
import { usePageTitle } from '@/hooks/usePageTitle';
import { taskApi } from '@/lib/services/task-service';
import useUserStore from '@/store/userStore';
import { Workflow } from '@/types/database';
import { getResearchDepthLabel, getTimePeriodLabel } from '@/utils/options';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';

// 消息数据类型定义
interface Message {
  id: number;
  message_id: string;
  conversation_id: string;
  task_id: string;
  message_type: 'human' | 'ai' | 'system' | 'tool';
  content: string;
  metadata: any;
  sequence_number: number;
  parent_message_id: string | null;
  created_at: string;
}

export default function TasksPage() {
  const router = useRouter();
  const { user, loading: authLoading, initialized } = useUserStore();

  // Set page title and description
  usePageTitle('任务列表', '管理和查看所有分析任务');
  const [tasks, setTasks] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMessages, setSelectedMessages] = useState<Message[]>([]);
  const [showMessagesModal, setShowMessagesModal] = useState(false);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [startingTask, setStartingTask] = useState<string | null>(null);
  const [selectedTask, setSelectedTask] = useState<Workflow | null>(null);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showAnalysisPreview, setShowAnalysisPreview] = useState<string | null>(null);
  const [taskAnalysisStatuses, setTaskAnalysisStatuses] = useState<Record<string, any>>({});
  const [activeTab, setActiveTab] = useState<'details' | 'workflow' | 'history'>('details');
  const [currentView, setCurrentView] = useState<ViewType>('grid');

  // 处理视图切换并保存到本地存储
  const handleViewChange = (view: ViewType) => {
    setCurrentView(view);
    localStorage.setItem('tasks-view-preference', view);
  };

  // 从本地存储加载视图偏好
  useEffect(() => {
    const savedView = localStorage.getItem('tasks-view-preference') as ViewType;
    if (savedView && (savedView === 'grid' || savedView === 'table')) {
      setCurrentView(savedView);
    }
  }, []);

  // 获取任务列表
  const fetchTasks = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);
    try {
      const response = await taskApi.getTasks();
      console.log(response);
      if (response.success) {
        setTasks(response.data);
      } else {
        const errorMessage = response.message || '获取任务列表失败';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取任务列表失败';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // 使用 useCallback 稳定 onStatusChange 函数引用
  const handleStatusChange = useCallback(
    (status: any) => {
      // 更新任务状态缓存
      if (status.taskId) {
        setTaskAnalysisStatuses((prev) => ({
          ...prev,
          [status.taskId!]: status,
        }));
      }

      // 如果分析完成，刷新任务列表
      if (status.status === 'completed' || status.status === 'failed') {
        fetchTasks();
      }
    },
    [fetchTasks]
  ); // fetchTasks 已经由 useCallback 包裹

  // 分析状态管理
  const analysisStatus = useAnalysisStatus({
    workflowId: showAnalysisPreview || undefined,
    autoRefresh: true,
    refreshInterval: 3000,
    onStatusChange: handleStatusChange,
  });

  // 查看任务详情
  const viewTaskDetails = (task: Workflow) => {
    setSelectedTask(task);
    setShowTaskModal(true);
  };

  // 查看任务消息
  const viewMessages = async (taskId: string) => {
    try {
      setLoadingMessages(true);
      const response = await fetch(
        `/api/database/messages?task_id=${taskId}&include_metadata=true`
      );
      if (!response.ok) {
        throw new Error('获取消息失败');
      }
      const data = await response.json();
      setSelectedMessages(data.messages || []);
      setShowMessagesModal(true);
    } catch (err) {
      toast.error('获取消息失败');
      console.error('获取消息失败:', err);
    } finally {
      setLoadingMessages(false);
    }
  };

  // 开始任务分析
  const startTask = async (task: Workflow) => {
    try {
      setStartingTask(task.workflow_id);

      // 使用新的分析状态Hook启动分析
      const result = await analysisStatus.startAnalysis(task.workflow_id, {
        ticker: task.ticker,
        researchDepth: task.research_depth || 'medium',
        analysisPeriod: task.analysis_period || '1d',
        config: {
          analysisType: 'comprehensive',
          includeRisk: true,
          includeSentiment: true,
          researchDepth: task.research_depth || 'medium',
          analysisPeriod: task.analysis_period || '1d',
        },
      });

      if ((result as any).success) {
        toast.success('分析启动成功！');
        // 移除手动刷新，交由 useAnalysisStatus hook 自动更新
        // fetchTasks();
        // 显示分析预览
        setShowAnalysisPreview(task.task_id);
      } else {
        toast.error(`启动分析失败: ${(result as any).message}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '启动分析失败';
      toast.error(`启动分析失败: ${errorMessage}`);
      console.error('启动分析失败:', err);
    } finally {
      setStartingTask(null);
    }
  };

  // 查看分析详情
  const viewAnalysisDetails = async (task: Workflow) => {
    if (task.status === 'running' || task.status === 'completed') {
      // 跳转到分析详情页面
      router.push(`/analysis/${task.workflow_id}`);
    } else {
      toast('该任务尚未开始分析');
    }
  };

  // 获取任务的分析状态
  const getTaskAnalysisStatus = (taskId: string) => {
    return taskAnalysisStatuses[taskId];
  };

  useEffect(() => {
    // 在 store 初始化之后，如果用户未登录，则重定向
    if (initialized && !user) {
      router.push('/login');
      return;
    }
    // 在 store 初始化之后，如果用户已登录，则获取任务
    if (initialized && user) {
      fetchTasks();
    }
  }, [initialized, user, router, fetchTasks]);

  if (!initialized || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600 dark:text-slate-400">加载任务列表中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">❌ 加载失败</div>
          <p className="text-slate-600 dark:text-slate-400 mb-4">{error}</p>
          <button
            onClick={fetchTasks}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">任务管理</h1>
          <p className="mt-2 text-slate-600 dark:text-slate-400">
            欢迎 {user?.username}，管理和操作您的分析任务
          </p>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
            <div className="text-2xl font-bold text-slate-900 dark:text-white">{tasks.length}</div>
            <div className="text-sm text-slate-600 dark:text-slate-400">总任务数</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
            <div className="text-2xl font-bold text-green-600">
              {tasks.filter((t) => t.status === 'completed').length}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">已完成</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
            <div className="text-2xl font-bold text-blue-600">
              {tasks.filter((t) => t.status === 'running').length}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">运行中</div>
          </div>
          <div className="bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700">
            <div className="text-2xl font-bold text-yellow-600">
              {tasks.filter((t) => t.status === 'pending').length}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">待处理</div>
          </div>
        </div>

        {/* 操作按钮和视图切换 */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={fetchTasks}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              刷新列表
            </button>
            <ViewToggle currentView={currentView} onViewChange={handleViewChange} />
          </div>
          <button
            onClick={() => router.push('/create-task')}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
          >
            创建新任务
          </button>
        </div>

        {/* 任务列表 */}
        {tasks.length === 0 ? (
          <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700">
            <div className="text-center py-12">
              <p className="text-slate-500 dark:text-slate-400 mb-4">暂无任务数据</p>
              <button
                onClick={() => router.push('/create-task')}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                创建第一个任务
              </button>
            </div>
          </div>
        ) : currentView === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tasks.map((task) => {
              const analysisStatus = getTaskAnalysisStatus(task.task_id);
              return (
                <TaskCard
                  key={task.id}
                  task={task}
                  analysisStatus={analysisStatus}
                  onViewAnalysis={viewAnalysisDetails}
                  onStartAnalysis={startTask}
                  onViewMessages={async (taskId) => router.push(`/messages?task_id=${taskId}`)}
                  startingTask={startingTask}
                />
              );
            })}
          </div>
        ) : (
          <TaskTable
            tasks={tasks}
            analysisStatuses={taskAnalysisStatuses}
            onViewAnalysis={viewAnalysisDetails}
            onStartAnalysis={startTask}
            onViewMessages={async (taskId) => router.push(`/messages?task_id=${taskId}`)}
            onViewDetails={viewTaskDetails}
            startingTask={startingTask}
          />
        )}

        {/* 任务详情模态框 */}
        {showTaskModal && selectedTask && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border border-slate-200 dark:border-slate-700 w-11/12 max-w-6xl shadow-lg rounded-md bg-white dark:bg-slate-800 min-h-[80vh]">
              <div className="mt-3 h-full flex flex-col">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white">任务详情</h3>
                  <button
                    onClick={() => setShowTaskModal(false)}
                    className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                  >
                    <span className="sr-only">关闭</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                {/* 标签页导航 */}
                <div className="border-b border-slate-200 dark:border-slate-600 mb-6">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      onClick={() => setActiveTab('details')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'details'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 hover:border-slate-300 dark:hover:border-slate-600'
                        }`}
                    >
                      基本信息
                    </button>
                    {/* 隐藏 */}
                    {/* <button
                      onClick={() => setActiveTab('workflow')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'workflow'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 hover:border-slate-300 dark:hover:border-slate-600'
                      }`}
                    >
                      工作流信息
                    </button>
                    <button
                      onClick={() => setActiveTab('history')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'history'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 hover:border-slate-300 dark:hover:border-slate-600'
                      }`}
                    >
                      执行历史
                    </button> */}
                  </nav>
                </div>

                {/* 标签页内容 */}
                <div className="flex-1 overflow-y-auto">
                  {activeTab === 'details' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            任务ID
                          </label>
                          <p className="mt-1 text-sm text-slate-900 dark:text-white font-mono">
                            {selectedTask.task_id}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            股票代码
                          </label>
                          <p className="mt-1 text-sm text-slate-900 dark:text-white font-semibold">
                            {selectedTask.ticker}
                          </p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                          标题
                        </label>
                        <p className="mt-1 text-sm text-slate-900 dark:text-white">
                          {selectedTask.title}
                        </p>
                      </div>

                      {selectedTask.description && (
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            描述
                          </label>
                          <p className="mt-1 text-sm text-slate-900 dark:text-white">
                            {selectedTask.description}
                          </p>
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            状态
                          </label>
                          <span
                            className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${selectedTask.status === 'completed'
                              ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                              : selectedTask.status === 'pending'
                                ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                                : selectedTask.status === 'running'
                                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                                  : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                              }`}
                          >
                            {selectedTask.status === 'completed'
                              ? '已完成'
                              : selectedTask.status === 'pending'
                                ? '待处理'
                                : selectedTask.status === 'running'
                                  ? '运行中'
                                  : '失败'}
                          </span>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            优先级
                          </label>
                          <p className="mt-1 text-sm text-slate-900 dark:text-white">
                            {selectedTask.priority || '-'}
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            研究深度
                          </label>
                          <p className="mt-1 text-sm text-slate-900 dark:text-white">
                            {(selectedTask.research_depth &&
                              getResearchDepthLabel(selectedTask.research_depth as any)) ||
                              '-'}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            分析周期
                          </label>
                          <p className="mt-1 text-sm text-slate-900 dark:text-white">
                            {(selectedTask.analysis_period &&
                              getTimePeriodLabel(selectedTask.analysis_period as any)) ||
                              '-'}
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            创建时间
                          </label>
                          <p className="mt-1 text-sm text-gray-900 dark:text-white">
                            {selectedTask.created_at
                              ? format(new Date(selectedTask.created_at), 'yyyy-MM-dd HH:mm:ss', {
                                locale: zhCN,
                              })
                              : '-'}
                          </p>
                        </div>
                        {/* <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">创建者</label>
                          <p className="mt-1 text-sm text-gray-900 dark:text-white">
                            {selectedTask.created_by || '-'}
                          </p>
                        </div> */}
                      </div>

                      {selectedTask.started_at && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            开始时间
                          </label>
                          <p className="mt-1 text-sm text-gray-900 dark:text-white">
                            {format(new Date(selectedTask.started_at), 'yyyy-MM-dd HH:mm:ss', {
                              locale: zhCN,
                            })}
                          </p>
                        </div>
                      )}

                      {selectedTask.completed_at && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            完成时间
                          </label>
                          <p className="mt-1 text-sm text-gray-900 dark:text-white">
                            {format(new Date(selectedTask.completed_at), 'yyyy-MM-dd HH:mm:ss', {
                              locale: zhCN,
                            })}
                          </p>
                        </div>
                      )}

                      {selectedTask.error_message && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            错误信息
                          </label>
                          <p className="mt-1 text-sm text-red-600 bg-red-50 p-2 rounded">
                            {selectedTask.error_message}
                          </p>
                        </div>
                      )}

                      {/* 分析预览 */}
                      {getTaskAnalysisStatus(selectedTask.task_id) && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            分析状态
                          </label>
                          <AnalysisPreview
                            status={getTaskAnalysisStatus(selectedTask.task_id)}
                            onViewDetails={() => {
                              setShowTaskModal(false);
                              viewAnalysisDetails(selectedTask);
                            }}
                          />
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === 'workflow' && (
                    <div>
                      {getTaskAnalysisStatus(selectedTask.task_id) ? (
                        <WorkflowInfo
                          status={getTaskAnalysisStatus(selectedTask.task_id)}
                          onViewAnalysis={() => {
                            setShowTaskModal(false);
                            viewAnalysisDetails(selectedTask);
                          }}
                        />
                      ) : (
                        <div className="text-center py-12 text-gray-500">
                          <p>该任务尚未开始分析，暂无工作流信息</p>
                          {selectedTask.status === 'pending' && (
                            <button
                              onClick={() => {
                                setShowTaskModal(false);
                                startTask(selectedTask);
                              }}
                              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                            >
                              开始分析
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  {/* 隐藏 */}
                  {/* {activeTab === 'history' && (
                    <div>
                      <TaskExecutionHistory taskId={selectedTask.task_id} />
                    </div>
                  )} */}
                </div>

                {/* 操作按钮 */}
                <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
                  {selectedTask.status === 'pending' && (
                    <button
                      onClick={() => {
                        setShowTaskModal(false);
                        startTask(selectedTask);
                      }}
                      disabled={startingTask === selectedTask.task_id}
                      className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
                    >
                      {startingTask === selectedTask.task_id ? '启动中...' : '开始分析'}
                    </button>
                  )}
                  {(selectedTask.status === 'running' || selectedTask.status === 'completed') && (
                    <button
                      onClick={() => {
                        setShowTaskModal(false);
                        viewAnalysisDetails(selectedTask);
                      }}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                      查看分析详情
                    </button>
                  )}
                  <button
                    onClick={() => setShowTaskModal(false)}
                    className="bg-slate-300 dark:bg-slate-600 text-slate-700 dark:text-slate-300 px-4 py-2 rounded-md hover:bg-slate-400 dark:hover:bg-slate-500"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 消息模态框 */}
        {showMessagesModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border border-slate-200 dark:border-slate-700 w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-slate-800">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-slate-900 dark:text-white">任务消息</h3>
                  <button
                    onClick={() => setShowMessagesModal(false)}
                    className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                  >
                    <span className="sr-only">关闭</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {selectedMessages.length === 0 ? (
                    <p className="text-slate-500 dark:text-slate-400 text-center py-8">
                      该任务暂无消息
                    </p>
                  ) : (
                    <div className="space-y-4">
                      {selectedMessages.map((message) => (
                        <div
                          key={message.id}
                          className={`p-4 rounded-lg ${message.message_type === 'human'
                            ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400'
                            : message.message_type === 'ai'
                              ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400'
                              : 'bg-slate-50 dark:bg-slate-800 border-l-4 border-slate-400'
                            }`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <span
                              className={`text-xs font-medium px-2 py-1 rounded ${message.message_type === 'human'
                                ? 'bg-blue-100 text-blue-800'
                                : message.message_type === 'ai'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                                }`}
                            >
                              {message.message_type === 'human'
                                ? '用户'
                                : message.message_type === 'ai'
                                  ? 'AI'
                                  : message.message_type === 'system'
                                    ? '系统'
                                    : '工具'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {format(new Date(message.created_at), 'yyyy-MM-dd HH:mm:ss', {
                                locale: zhCN,
                              })}
                            </span>
                          </div>
                          <div className="text-sm text-gray-900 whitespace-pre-wrap">
                            {message.content}
                          </div>
                          {message.metadata && (
                            <details className="mt-2">
                              <summary className="text-xs text-gray-500 cursor-pointer">
                                元数据
                              </summary>
                              <pre className="text-xs text-gray-600 mt-1 bg-gray-100 p-2 rounded overflow-x-auto">
                                {JSON.stringify(message.metadata, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
