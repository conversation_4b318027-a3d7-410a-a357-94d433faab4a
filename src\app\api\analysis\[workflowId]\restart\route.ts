import { analysisService } from '@/lib/analysis-service';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(
    request: NextRequest,
    { params }: { params: Promise<{ workflowId: string }> }
) {
    try {
        const { workflowId } = await params;

        if (!workflowId) {
            return NextResponse.json(
                {
                    success: false,
                    error: '工作流ID不能为空',
                    timestamp: new Date().toISOString(),
                },
                { status: 400 }
            );
        }

        // First stop the current analysis if it's running
        try {
            await analysisService.stopAnalysis(workflowId);
        } catch (error) {
            // If stopping fails because analysis is not running, that's okay
            console.log('Analysis was not running, proceeding with restart');
        }

        // Get the analysis status to get the task ID
        const analysisStatus = await analysisService.getAnalysisStatus(workflowId);

        if (!analysisStatus.taskId) {
            return NextResponse.json(
                {
                    success: false,
                    error: '无法找到关联的任务ID',
                    timestamp: new Date().toISOString(),
                },
                { status: 400 }
            );
        }

        // Restart the analysis by executing it again
        const result = await analysisService.executeAnalysis({
            taskId: analysisStatus.taskId,
        });

        return NextResponse.json({
            success: true,
            data: result,
            message: '分析已重启',
            timestamp: new Date().toISOString(),
        });
    } catch (error: any) {
        console.error('Restart analysis error:', error);

        // Handle specific error types
        if (error.code === 'ANALYSIS_NOT_FOUND') {
            return NextResponse.json(
                {
                    success: false,
                    error: '分析不存在',
                    timestamp: new Date().toISOString(),
                },
                { status: 404 }
            );
        }

        if (error.code === 'TASK_NOT_FOUND') {
            return NextResponse.json(
                {
                    success: false,
                    error: '关联任务不存在',
                    timestamp: new Date().toISOString(),
                },
                { status: 404 }
            );
        }

        return NextResponse.json(
            {
                success: false,
                error: error.message || '重启分析失败',
                timestamp: new Date().toISOString(),
            },
            { status: 500 }
        );
    }
}