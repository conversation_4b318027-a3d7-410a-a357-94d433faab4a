import { api } from '../api';
import { ApiResponse } from '../api';

// 认证相关接口
export interface User {
  id: number;
  email: string;
  username: string;
  email_verified: boolean;
  avatar_url?: string;
  role: string;
  created_at: string;
  last_login_at?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  sessionId: string;
}

// 认证API方法
export const authApi = {
  // 用户登录
  login: (credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    return api.post('/api/auth/login', credentials);
  },

  // 用户注册
  register: (userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> => {
    return api.post('/api/auth/register', userData);
  },

  // 用户登出
  logout: (): Promise<ApiResponse<{ message: string }>> => {
    return api.post('/api/auth/logout');
  },
};

// 获取当前用户信息
export const getCurrentUser = (): Promise<ApiResponse<User>> => {
  return api.get('/api/auth/me');
};