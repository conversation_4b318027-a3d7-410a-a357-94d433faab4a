'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { analysisApi } from '@/lib/services/analysis-service';
import { Workflow } from '@/types/database';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useEffect, useState } from 'react';

interface AnalysisComparisonSelectorProps {
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  maxSelection?: number;
  onStartAnalysis?: (workflowId: string) => void;
}

const STATUS_CONFIG = {
  pending: { label: '待处理', color: 'default' },
  running: { label: '运行中', color: 'secondary' },
  completed: { label: '已完成', color: 'success' },
  failed: { label: '失败', color: 'destructive' },
  cancelled: { label: '已取消', color: 'outline' },
} as const;

export function AnalysisComparisonSelector({
  selectedIds,
  onSelectionChange,
  maxSelection = 5,
  onStartAnalysis,
}: AnalysisComparisonSelectorProps) {
  const [availableAnalyses, setAvailableAnalyses] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>(['completed']);

  // 加载可用的分析列表
  const loadAvailableAnalyses = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        limit: 50, // 限制数量以提高性能
      };

      // 只有当状态筛选不为空时才添加status参数，并用逗号拼接
      if (statusFilter.length > 0) {
        params.status = statusFilter.join(',');
      }

      const result = await analysisApi.getAnalysisHistory(params);

      if (!result.success) {
        throw new Error(result.message || '获取分析列表失败');
      }

      setAvailableAnalyses(result.data.workflows || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadAvailableAnalyses();
  }, [statusFilter]);

  // 处理选择
  const handleToggleSelection = (workflowId: string) => {
    if (selectedIds.includes(workflowId)) {
      // 取消选择
      onSelectionChange(selectedIds.filter((id) => id !== workflowId));
    } else {
      // 添加选择
      if (selectedIds.length >= maxSelection) {
        alert(`最多只能选择 ${maxSelection} 个分析进行对比`);
        return;
      }
      onSelectionChange([...selectedIds, workflowId]);
    }
  };

  // 清除所有选择
  const handleClearSelection = () => {
    onSelectionChange([]);
  };

  // 筛选分析列表
  const filteredAnalyses = availableAnalyses.filter((analysis) => {
    // 应用状态筛选
    if (statusFilter.length > 0 && !statusFilter.includes(analysis.status)) {
      return false;
    }

    // 应用搜索词筛选
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return (
        analysis.ticker.toLowerCase().includes(term) || analysis.title.toLowerCase().includes(term)
      );
    }
    return true;
  });

  // 处理状态筛选
  const handleStatusFilterChange = (status: string) => {
    if (statusFilter.includes(status)) {
      setStatusFilter((prev) => prev.filter((s) => s !== status));
    } else {
      setStatusFilter((prev) => [...prev, status]);
    }
  };

  return (
    <Card className="p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2 dark:text-slate-100">
          选择要对比的分析
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          最多可选择 {maxSelection} 个分析进行对比。已选择: {selectedIds.length}/{maxSelection}
        </p>
      </div>

      {/* 筛选控件 */}
      <div className="mb-4 space-y-3">
        {/* 搜索框 */}
        <div>
          <input
            type="text"
            placeholder="搜索股票代码或标题..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-slate-800 dark:text-slate-100"
          />
        </div>

        {/* 状态筛选 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700 dark:text-slate-100">状态筛选:</span>
          {Object.entries(STATUS_CONFIG).map(([status, config]) => (
            <button
              key={status}
              onClick={() => handleStatusFilterChange(status)}
              className={`px-3 py-1 text-xs rounded-full border transition-colors ${statusFilter.includes(status)
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700'
                : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100 hover:border-gray-300 dark:bg-slate-800 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-700'
                }`}
            >
              {config.label}
            </button>
          ))}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {selectedIds.length > 0 && (
              <Button variant="secondary" size="sm" onClick={handleClearSelection} className="dark:bg-slate-800 dark:text-slate-100 dark:border-slate-700 dark:hover:bg-slate-700">
                清除选择
              </Button>
            )}
            <Button
              variant="secondary"
              size="sm"
              onClick={loadAvailableAnalyses}
              disabled={loading}
              className="dark:bg-slate-800 dark:text-slate-100 dark:border-slate-700 dark:hover:bg-slate-700"
            >
              刷新列表
            </Button>
          </div>

          {selectedIds.length >= 2 && <Badge variant="secondary">可以开始对比</Badge>}
        </div>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="md" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">加载分析列表...</span>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md dark:bg-red-900/20 dark:border-red-800">
          <div className="flex items-center">
            <div className="text-red-500 text-lg mr-2 dark:text-red-400">⚠️</div>
            <div>
              <h4 className="text-red-800 font-medium dark:text-red-300">加载失败</h4>
              <p className="text-red-700 text-sm dark:text-red-400">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 分析列表 */}
      {!loading && !error && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {filteredAnalyses.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>没有找到符合条件的分析</p>
              <p className="text-sm mt-1">请尝试调整筛选条件</p>
            </div>
          ) : (
            filteredAnalyses.map((analysis) => {
              const isSelected = selectedIds.includes(analysis.workflow_id);
              const statusConfig = STATUS_CONFIG[analysis.status];

              return (
                <div
                  key={analysis.workflow_id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all ${isSelected
                    ? 'border-blue-500 bg-blue-50 dark:border-blue-700 dark:bg-blue-900/20'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 dark:border-slate-700 dark:hover:border-slate-600 dark:hover:bg-slate-800'
                    }`}
                  onClick={() => handleToggleSelection(analysis.workflow_id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      {/* 选择框 */}
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => { }} // 由父级div的onClick处理
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-slate-600 dark:bg-slate-800"
                      />

                      {/* 分析信息 */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-semibold text-blue-600 dark:text-blue-400">{analysis.ticker}</span>
                          <Badge variant={statusConfig.color}>{statusConfig.label}</Badge>
                          {onStartAnalysis && (
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation();
                                onStartAnalysis(analysis.workflow_id);
                              }}
                              className="dark:bg-slate-700 dark:text-slate-100 dark:border-slate-600"
                            >
                              开始分析
                            </Button>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 line-clamp-1 dark:text-gray-300">{analysis.title}</p>
                        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                          <span>
                            创建:{' '}
                            {format(new Date(analysis.created_at), 'MM-dd HH:mm', { locale: zhCN })}
                          </span>
                          {analysis.completed_at && (
                            <span>
                              完成:{' '}
                              {format(new Date(analysis.completed_at), 'MM-dd HH:mm', {
                                locale: zhCN,
                              })}
                            </span>
                          )}
                          <span>{analysis.report_count} 个报告</span>
                        </div>
                      </div>
                    </div>

                    {/* 选择指示器 */}
                    {isSelected && <div className="text-blue-600 text-lg dark:text-blue-400">✓</div>}
                  </div>
                </div>
              );
            })
          )}
        </div>
      )}

      {/* 选择摘要 */}
      {selectedIds.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-slate-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">已选择 {selectedIds.length} 个分析</span>
            {selectedIds.length >= 2 && (
              <span className="text-sm text-green-600 font-medium dark:text-green-400">✓ 可以开始对比</span>
            )}
          </div>
        </div>
      )}
    </Card>
  );
}