import { analysisService } from '@/lib/analysis-service';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ workflowId: string }> }
) {
    try {
        const { workflowId } = await params;

        if (!workflowId) {
            return NextResponse.json(
                {
                    success: false,
                    error: '工作流ID不能为空',
                    timestamp: new Date().toISOString(),
                },
                { status: 400 }
            );
        }

        // Get analysis status which includes all reports
        const analysisStatus = await analysisService.getAnalysisStatus(workflowId);

        // Extract reports
        const reports = {
            analystReports: analysisStatus.analystReports,
            researchReports: analysisStatus.researchReports,
            finalDecision: analysisStatus.finalDecision,
        };

        return NextResponse.json({
            success: true,
            data: reports,
            timestamp: new Date().toISOString(),
        });
    } catch (error: any) {
        console.error('Get reports error:', error);

        // Handle specific error types
        if (error.code === 'ANALYSIS_NOT_FOUND') {
            return NextResponse.json(
                {
                    success: false,
                    error: '分析不存在',
                    timestamp: new Date().toISOString(),
                },
                { status: 404 }
            );
        }

        return NextResponse.json(
            {
                success: false,
                error: error.message || '获取分析报告失败',
                timestamp: new Date().toISOString(),
            },
            { status: 500 }
        );
    }
}