# AKShare 后端服务

这是一个基于 FastAPI 的 Python 后端服务，提供中国金融市场数据的 RESTful API 接口。该服务将 akshare 库的功能封装为 HTTP API，供前端应用调用。

## 🚀 主要特性

- **RESTful API**: 提供标准的 HTTP API 接口
- **高性能**: 基于 FastAPI 框架，支持异步处理
- **容器化部署**: 支持 Docker 容器化部署
- **自动文档**: 自动生成 Swagger UI 和 ReDoc 文档
- **健康检查**: 内置健康检查端点
- **错误处理**: 完善的错误处理和日志记录

## 📋 API 接口

### 基础接口

- `GET /` - 服务信息
- `GET /health` - 健康检查
- `GET /docs` - Swagger UI 文档
- `GET /redoc` - ReDoc 文档

### 股票数据接口

- `POST /api/stock/history` - 获取股票历史数据
- `POST /api/stock/news` - 获取股票新闻
- `POST /api/stock/fundamental` - 获取股票基本面数据
- `POST /api/stock/realtime` - 获取股票实时数据
- `GET /api/market/overview` - 获取市场概览

## 🛠️ 本地开发

### 环境要求

- Python 3.11+
- pip 或 conda

### 安装依赖

```bash
cd backend/akshare-service
pip install -r requirements.txt
```

### 启动服务

```bash
# 开发模式（支持热重载）
uvicorn app.main:app --host 0.0.0.0 --port 5000 --reload

# 或者直接运行
python app/main.py
```

服务启动后可以访问：
- API 服务: http://localhost:5000
- API 文档: http://localhost:5000/docs
- 健康检查: http://localhost:5000/health

## 🐳 Docker 部署

### 构建镜像

```bash
# 在项目根目录执行
docker build -t akshare-backend backend/akshare-service/
```

### 运行容器

```bash
# 单独运行 AKShare 后端
docker run -p 5000:5000 akshare-backend

# 或使用 docker-compose
docker-compose -f docker/docker-compose.akshare.yml up -d
```

### 完整环境启动

```bash
# 启动完整环境（前端 + 后端 + 数据库）
cd docker
./start.sh  # Linux/Mac
# 或
./start.ps1  # Windows

# 选择选项 5: 前端 + AKShare后端
```

## 📝 API 使用示例

### 获取股票历史数据

```bash
curl -X POST "http://localhost:5000/api/stock/history" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "period": "daily",
    "start_date": "20240101",
    "end_date": "20241231"
  }'
```

### 获取股票新闻

```bash
curl -X POST "http://localhost:5000/api/stock/news" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "limit": 10
  }'
```

### 获取股票基本面数据

```bash
curl -X POST "http://localhost:5000/api/stock/fundamental" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "indicator": "all",
    "period_type": "按报告期",
    "time_period": "1y"
  }'
```

参数说明：
- `symbol`: 股票代码（必填）
- `indicator`: 数据指标类型（可选，默认为 all）
  - `all` - 所有基本面数据
  - `financial_analysis` - 财务分析指标
  - `balance_sheet` - 资产负债表
  - `profit_sheet` - 利润表
  - `cash_flow_sheet` - 现金流量表
- `period_type`: 报告期类型（可选，默认为 按报告期）
  - `按报告期` - 按报告期
  - `按年度` - 按年度
  - `按单季度` - 按单季度
- `time_period`: 时间周期（可选）
  - 格式：数字+单位，如 `1y`（1年）、`6m`（6个月）、`2q`（2个季度）
  - 根据报告期类型和周期自动筛选数据：
    - 按单季度且周期≥1年：返回最近4个季度数据
    - 按年度且周期≥1年：返回最近的年份数据
    - 其他情况：至少返回2条数据

### 健康检查

```bash
curl http://localhost:5000/health
```

## 🔧 配置说明

### 环境变量

复制 `.env.example` 为 `.env` 并根据需要修改：

```bash
cp .env.example .env
```

主要配置项：
- `HOST`: 服务监听地址（默认: 0.0.0.0）
- `PORT`: 服务端口（默认: 5000）
- `DEBUG`: 调试模式（默认: false）
- `LOG_LEVEL`: 日志级别（默认: INFO）

## 🔗 与前端集成

前端的 akshare 适配器已经更新为调用此后端服务。确保：

1. 后端服务正在运行（http://localhost:5000）
2. 前端环境变量正确配置：
   ```env
   AKSHARE_API_URL=http://localhost:5000
   # 或在 Docker 环境中
   AKSHARE_API_URL=http://akshare-backend:5000
   ```

## 🧪 测试

### 手动测试

访问 http://localhost:5000/docs 使用 Swagger UI 进行交互式测试。

### 自动化测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio

# 运行测试
pytest
```

## 📊 监控和日志

### 查看日志

```bash
# Docker 环境
docker-compose logs -f akshare-backend

# 本地开发
# 日志会输出到控制台
```

### 健康检查

服务提供健康检查端点，可用于监控：

```bash
curl http://localhost:5000/health
```

返回示例：
```json
{
  "status": "healthy",
  "akshare": "available"
}
```

## 🚨 故障排除

### 常见问题

1. **端口冲突**: 确保 5000 端口未被占用
2. **依赖安装失败**: 检查 Python 版本和网络连接
3. **akshare 数据获取失败**: 检查网络连接和 akshare 服务状态

### 调试模式

设置环境变量 `DEBUG=true` 启用详细日志：

```bash
DEBUG=true python app/main.py
```

## 📈 性能优化

- 考虑添加 Redis 缓存来缓存频繁请求的数据
- 使用连接池优化数据库连接
- 实施请求限流防止过载

## 🔒 安全考虑

- 在生产环境中限制 CORS 允许的域名
- 添加 API 认证和授权
- 使用 HTTPS 加密传输
- 定期更新依赖包

## 📚 相关文档

- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [AKShare 官方文档](https://akshare.akfamily.xyz/)
- [Docker 部署指南](../docker/README.md)
