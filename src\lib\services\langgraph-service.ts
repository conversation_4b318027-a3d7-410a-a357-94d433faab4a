import { api } from '../api';
import { ApiResponse } from '../api';
import { CreateTaskRequest } from '@/types/database';

// LangGraph 工作流相关接口
export interface WorkflowStatus {
  workflow_id: string;
  task_id: string;
  ticker: string;
  current_stage: string;
  workflow_status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  workflow_created_at: string;
  workflow_completed_at?: string;

  // 完成状态
  fundamental_completed: boolean;
  technical_completed: boolean;
  sentiment_completed: boolean;
  news_completed: boolean;
  bull_completed: boolean;
  bear_completed: boolean;
  consensus_completed: boolean;
  risk_assessment_completed: boolean;
  decision_completed: boolean;

  // 统计信息
  total_debate_messages: number;
  max_debate_round: number;
  total_workflow_messages: number;
  duration_seconds: number;
}

export interface WorkflowDetails {
  workflow: any;
  status: WorkflowStatus;
  analysts: {
    fundamental?: any;
    technical?: any;
    sentiment?: any;
    news?: any;
  };
  researchers: {
    bull?: any;
    bear?: any;
  };
  debate: {
    records: any[];
    rounds: number;
    totalArguments: number;
  };
  consensus?: any;
  risk?: any;
  decision?: any;
  messages: any[];
  snapshots: any[];
  statistics: {
    totalMessages: number;
    totalDebateRounds: number;
    durationSeconds: number;
    completionRate: {
      [key: string]: boolean;
    };
  };
}

export interface CreateWorkflowRequest {
  workflow_id: string;
  task_id: string;
  ticker: string;
  config?: any;
}

export interface UpdateWorkflowStageRequest {
  workflow_id: string;
  current_stage: string;
  progress: number;
  status: WorkflowStatus['workflow_status'];
}

export interface QueryWorkflowsOptions {
  task_id?: string;
  ticker?: string;
  status?: string;
  limit?: number;
  offset?: number;
}

// LangGraph 工作流 API 方法
export const langGraphApi = {
  // 创建工作流
  createWorkflow: (data: CreateTaskRequest): Promise<ApiResponse<{ workflow_id: string }>> => {
    return api.post('/api/tasks/create', data);
  },

  // 更新工作流状态
  updateWorkflowStage: (
    data: UpdateWorkflowStageRequest
  ): Promise<ApiResponse<{ workflow_id: string }>> => {
    return api.put('/api/langgraph/workflow', data);
  },

  // 获取工作流状态
  getWorkflowStatus: (workflowId?: string): Promise<ApiResponse<WorkflowDetails>> => {
    const params: Record<string, string> = {};
    if (workflowId) params.workflow_id = workflowId;

    return api.get('/api/langgraph/workflow/status', { params });
  },

  // 查询工作流列表
  queryWorkflows: (options: QueryWorkflowsOptions = {}): Promise<ApiResponse<any[]>> => {
    return api.get('/api/langgraph/workflow', { params: options });
  },

  // 获取工作流详细信息（包含所有相关数据）
  getWorkflowDetails: (workflowId: string): Promise<ApiResponse<WorkflowDetails>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/details`);
  },

  // 获取分析师结果
  getAnalystResults: (workflowId: string, analystType?: string): Promise<ApiResponse<any[]>> => {
    const params = analystType ? { analyst_type: analystType } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/analysts`, { params });
  },

  // 获取研究员结果
  getResearcherResults: (
    workflowId: string,
    researcherType?: string
  ): Promise<ApiResponse<any[]>> => {
    const params = researcherType ? { researcher_type: researcherType } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/researchers`, { params });
  },

  // 获取辩论记录
  getDebateRecords: (workflowId: string, round?: number): Promise<ApiResponse<any[]>> => {
    const params = round ? { round: round.toString() } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/debate`, { params });
  },

  // 获取共识评估
  getConsensusEvaluation: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/consensus`);
  },

  // 获取风险评估
  getRiskAssessment: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/risk`);
  },

  // 获取交易决策
  getTradingDecision: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/langgraph/workflow/${workflowId}/decision`);
  },

  // 获取工作流消息
  getWorkflowMessages: (workflowId: string, stageName?: string): Promise<ApiResponse<any[]>> => {
    const params = stageName ? { stage_name: stageName } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/messages`, { params });
  },

  // 获取状态快照
  getStateSnapshots: (workflowId: string, stageName?: string): Promise<ApiResponse<any[]>> => {
    const params = stageName ? { stage_name: stageName } : {};
    return api.get(`/api/langgraph/workflow/${workflowId}/snapshots`, { params });
  },
};