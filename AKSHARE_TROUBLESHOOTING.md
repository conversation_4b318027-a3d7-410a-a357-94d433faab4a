# AKShare 服务故障排除指南

## 问题：GET /health HTTP/1.1 404 Not Found

### 可能原因

1. **服务未启动**
2. **端口冲突**
3. **路径配置错误**
4. **依赖缺失**
5. **防火墙阻止**

### 诊断步骤

#### 1. 检查服务状态

```bash
# 快速检查
python check_service_status.py

# 详细诊断
python debug_akshare_connection.py
```

#### 2. 检查端口占用

**Windows:**
```cmd
netstat -ano | findstr :5000
```

**Linux/Mac:**
```bash
lsof -i :5000
```

#### 3. 启动服务

```bash
# 方法1: 直接启动
cd backend/akshare-service
python app/main.py

# 方法2: 使用启动脚本
cd backend/akshare-service
python start_service.py

# 方法3: 使用 uvicorn
cd backend/akshare-service
uvicorn app.main:app --host 0.0.0.0 --port 5000
```

#### 4. 测试服务

```bash
# 简单测试
python test_service_simple.py

# 完整测试
python test_akshare_service.py
```

### 常见解决方案

#### 解决方案1: 确保依赖已安装

```bash
cd backend/akshare-service
pip install -r requirements.txt
```

#### 解决方案2: 检查Python路径

```bash
cd backend/akshare-service
python -c "import sys; print(sys.path)"
python -c "from app.main import app; print('导入成功')"
```

#### 解决方案3: 使用不同端口

如果端口5000被占用，修改端口：

```python
# 在 backend/akshare-service/app/main.py 最后
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5001)  # 改为5001
```

同时更新前端配置：
```env
BACK_END_URL=http://localhost:5001
```

#### 解决方案4: 检查防火墙

**Windows:**
```cmd
netsh advfirewall firewall add rule name="AKShare Service" dir=in action=allow protocol=TCP localport=5000
```

**Linux:**
```bash
sudo ufw allow 5000
```

#### 解决方案5: 使用Docker运行

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY app/ ./app/
EXPOSE 5000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "5000"]
```

```bash
# 构建和运行
docker build -t akshare-service .
docker run -p 5000:5000 akshare-service
```

### 验证修复

1. **服务启动成功**
   ```bash
   curl http://localhost:5000/
   # 应该返回: {"service": "AKShare 数据服务", "version": "1.1.0", "status": "运行中"}
   ```

2. **健康检查通过**
   ```bash
   curl http://localhost:5000/health
   # 应该返回: {"status": "healthy", "timestamp": "..."}
   ```

3. **API文档可访问**
   ```bash
   curl -I http://localhost:5000/docs
   # 应该返回: HTTP/1.1 200 OK
   ```

4. **前端连接成功**
   ```typescript
   import { akshareAdapter } from '@/lib/akshare/adapter';
   
   const health = await akshareAdapter.healthCheck();
   console.log(health); // {status: "healthy", latency: 123}
   ```

### 日志分析

#### 启动日志示例（正常）
```
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:5000 (Press CTRL+C to quit)
```

#### 错误日志示例
```
ERROR:    [Errno 98] Address already in use
# 解决：端口被占用，更换端口或终止占用进程

ModuleNotFoundError: No module named 'app'
# 解决：检查Python路径和文件结构

ImportError: cannot import name 'app' from 'app.main'
# 解决：检查app/main.py文件是否存在且语法正确
```

### 联系支持

如果以上方法都无法解决问题，请提供以下信息：

1. 操作系统版本
2. Python版本
3. 完整的错误日志
4. 端口占用情况
5. 服务启动命令和输出

```bash
# 收集系统信息
python --version
pip list | grep -E "(fastapi|uvicorn|akshare)"
netstat -tulpn | grep :5000  # Linux
netstat -ano | findstr :5000  # Windows
```