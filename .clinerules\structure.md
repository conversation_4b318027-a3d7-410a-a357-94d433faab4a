# TradingAgents Frontend 项目结构

## 目录结构概览

```
trading-agents-frontend/
├── src/                    # 源代码目录
│   ├── app/                # Next.js App Router
│   ├── components/         # React 组件
│   ├── hooks/              # 自定义 Hooks
│   ├── lib/                # 工具库
│   ├── store/              # 状态管理
│   ├── types/              # TypeScript 类型定义
│   └── utils/              # 工具函数
├── database/               # 数据库相关
├── docker/                 # Docker 配置
├── docs/                   # 项目文档
├── public/                 # 静态资源
├── scripts/                # 脚本文件
└── tests/                  # 测试文件
```

## 核心目录详解

### `src/app` - Next.js App Router

App Router 是 Next.js 的新路由系统，基于文件系统路由：

```
src/app/
├── analysis/[id]/         # 动态分析页面
├── api/                   # API 路由
│   ├── database/          # 数据库操作接口
│   └── langgraph/         # LangGraph 分析接口
├── create-task/           # 任务创建页面
├── messages/              # 消息查询页面
├── tasks/                 # 任务管理页面
├── globals.css            # 全局样式
├── layout.tsx             # 根布局（包含Header和Footer）
└── page.tsx               # 首页（双屏设计）
```

### `src/components` - React 组件

组件按功能和用途分类：

```
src/components/
├── dashboard/             # 仪表板组件
│   ├── TradingDashboard.tsx
│   ├── AnalysisProgress.tsx
│   ├── AgentStatusPanel.tsx
│   ├── RealtimeDataPanel.tsx
│   ├── ReportViewer.tsx
│   └── TradingDecision.tsx
├── database/              # 数据库查询组件
├── examples/              # 示例组件
├── langgraph/             # LangGraph 智能体组件
├── layout/                # 布局组件
│   ├── Header.tsx
│   └── Footer.tsx
├── ui/                    # 基础 UI 组件
│   ├── Button.tsx
│   ├── Card.tsx
│   └── ...
├── common/                # 通用组件
└── welcome/               # 欢迎页组件
    ├── WelcomeScreen.tsx
    └── AnalysisConfigForm.tsx
```

### `src/hooks` - 自定义 Hooks

```
src/hooks/
├── useAnalysisTask.ts     # 分析任务管理
├── useLangGraphAgent.ts   # LangGraph 智能体
├── useWebSocket.ts        # WebSocket 连接
└── ...
```

### `src/lib` - 工具库

```
src/lib/
├── api.ts                # API 接口封装
├── db.ts                 # 数据库连接
├── langgraph.ts          # LangGraph 客户端
├── langgraph-server.ts   # LangGraph 服务端
└── task-flow-database.ts # 任务流数据库操作
```

### `src/store` - 状态管理

```
src/store/
├── analysisStore.ts      # 分析状态管理
├── uiStore.ts            # UI 状态管理
└── ...
```

### `src/types` - TypeScript 类型定义

```
src/types/
├── index.ts             # 核心类型定义
├── database.ts          # 数据库类型定义
├── analysis.ts          # 分析相关类型
└── ...
```

### `src/utils` - 工具函数

```
src/utils/
├── constants.ts         # 常量定义
├── formatters.ts        # 格式化函数
├── validators.ts        # 验证函数
└── ...
```

### `database` - 数据库相关

```
database/
├── schema.sql           # 数据库结构
├── init_database.sql    # 初始化脚本
├── migration.sql        # 迁移脚本
└── README.md            # 数据库说明
```

### `docker` - Docker 配置

```
docker/
├── docker-compose.yml           # 主 Docker Compose 配置
├── docker-compose.dev.yml       # 开发环境配置
├── docker-compose.prod.yml      # 生产环境配置
├── Dockerfile                   # 主 Dockerfile
├── Dockerfile.dev               # 开发环境 Dockerfile
├── nginx.conf                   # Nginx 配置
└── README.md                    # Docker 说明
```

### `docs` - 项目文档

```
docs/
├── NEW_TASK_FLOW.md      # 新任务流程文档
├── SERVER_API_USAGE.md   # 服务端API使用指南
└── TASK_MANAGEMENT.md    # 任务管理文档
```

## 文件命名规范

### 组件文件

- 使用 PascalCase 命名组件文件
- 每个组件一个文件
- 文件名与组件名一致

```
Button.tsx
Card.tsx
UserProfile.tsx
```

### 页面文件

- 使用 Next.js App Router 约定
- 页面组件使用 `page.tsx`
- 布局组件使用 `layout.tsx`
- 加载状态使用 `loading.tsx`
- 错误处理使用 `error.tsx`

```
app/dashboard/page.tsx
app/dashboard/layout.tsx
app/dashboard/loading.tsx
app/dashboard/error.tsx
```

### 工具和钩子文件

- 使用 camelCase 命名
- 钩子以 `use` 开头

```
useAnalysisTask.ts
formatDate.ts
constants.ts
```

## 导入路径别名

项目使用 TypeScript 路径别名简化导入：

```typescript
// tsconfig.json 配置
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@components/*": ["./src/components/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@lib/*": ["./src/lib/*"],
      "@utils/*": ["./src/utils/*"],
      "@types/*": ["./src/types/*"],
      "@store/*": ["./src/store/*"]
    }
  }
}
```

使用示例：

```typescript
// 导入组件
import { Button } from '@components/ui/Button';

// 导入钩子
import { useAnalysisTask } from '@hooks/useAnalysisTask';

// 导入工具函数
import { formatDate } from '@utils/formatters';
```

## 代码组织原则

### 组件组织

- 按功能和用途分类组件
- 相关组件放在同一目录下
- 共享组件放在 `ui` 或 `common` 目录下

### 状态管理

- 全局状态使用 Zustand
- 服务器状态使用 TanStack Query
- 本地组件状态使用 React useState

### API 调用

- API 调用封装在 `lib/api.ts` 中
- 使用 TanStack Query 进行数据获取和缓存
- WebSocket 连接封装在 `hooks/useWebSocket.ts` 中

## 数据流

```
用户交互
   ↓
React 组件
   ↓
Hooks / 状态管理
   ↓
API 调用 / WebSocket
   ↓
后端服务
   ↓
数据库 / LangGraph
```

## 模块依赖关系

- **组件** 依赖于 **Hooks** 和 **状态管理**
- **Hooks** 依赖于 **API 调用** 和 **工具函数**
- **API 调用** 依赖于 **类型定义**
- **状态管理** 依赖于 **类型定义**

## 最佳实践

1. **组件设计**:

   - 保持组件小而专注
   - 使用组合而非继承
   - 遵循单一职责原则

2. **状态管理**:

   - 本地状态用 useState
   - 共享状态用 Zustand
   - 服务器状态用 TanStack Query

3. **性能优化**:

   - 使用 React.memo 避免不必要的重渲染
   - 使用 useMemo 和 useCallback 缓存计算结果和函数
   - 使用 Next.js Image 组件优化图片

4. **代码分割**:
   - 使用动态导入 (dynamic imports)
   - 按路由分割代码
   - 懒加载非关键组件
