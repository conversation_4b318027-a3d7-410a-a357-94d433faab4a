'use client';


import { AgentStatus, AnalysisConfig, AnalysisReport, AnalysisState, tradingApi, TradingDecision } from '@/lib/services/analysis-service';
import { TradingWebSocket } from '@/lib/services/websocket-service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';

export function useTradingAnalysis(config: AnalysisConfig) {
  const [analysisId, setAnalysisId] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisState, setAnalysisState] = useState<AnalysisState>({
    currentStage: 'initialization',
    progress: 0,
    isComplete: false,
  });
  const [agentStatuses, setAgentStatuses] = useState<AgentStatus[]>([]);
  const [reports, setReports] = useState<AnalysisReport[]>([]);
  const [finalDecision, setFinalDecision] = useState<TradingDecision | null>(null);

  const wsRef = useRef<TradingWebSocket | null>(null);
  const queryClient = useQueryClient();

  // 开始分析的 mutation
  const startAnalysisMutation = useMutation({
    mutationFn: tradingApi.startAnalysis,
    onSuccess: (response) => {
      if ((response as any).success) {
        setAnalysisId((response as any).data.analysisId);
        setIsAnalyzing(true);
        toast.success('分析已开始');
      } else {
        toast.error((response as any).message || '启动分析失败');
      }
    },
    onError: (error) => {
      console.error('Failed to start analysis:', error);
      toast.error('启动分析失败');
    },
  });

  // 停止分析的 mutation
  const stopAnalysisMutation = useMutation({
    mutationFn: (id: string) => tradingApi.stopAnalysis(id),
    onSuccess: () => {
      setIsAnalyzing(false);
      toast.success('分析已停止');
    },
    onError: (error) => {
      console.error('Failed to stop analysis:', error);
      toast.error('停止分析失败');
    },
  });

  // 获取分析状态
  const { data: statusData } = useQuery({
    queryKey: ['analysisStatus', analysisId],
    queryFn: () => (analysisId ? tradingApi.getAnalysisStatus(analysisId) : null),
    enabled: !!analysisId && isAnalyzing,
    refetchInterval: 2000, // 每2秒轮询一次
  });

  // 获取代理状态
  const { data: agentsData } = useQuery({
    queryKey: ['agentStatuses', analysisId],
    queryFn: () => (analysisId ? tradingApi.getAgentStatuses(analysisId) : null),
    enabled: !!analysisId,
    refetchInterval: 3000, // 每3秒轮询一次
  });

  // 获取分析报告
  const { data: reportsData } = useQuery({
    queryKey: ['reports', analysisId],
    queryFn: () => (analysisId ? tradingApi.getReports(analysisId) : null),
    enabled: !!analysisId,
    refetchInterval: 5000, // 每5秒轮询一次
  });

  // 获取交易决策
  const { data: decisionData } = useQuery({
    queryKey: ['tradingDecision', analysisId],
    queryFn: () => (analysisId ? tradingApi.getTradingDecision(analysisId) : null),
    enabled: !!analysisId,
    refetchInterval: 5000, // 每5秒轮询一次
  });

  // WebSocket 连接管理
  useEffect(() => {
    if (analysisId && isAnalyzing) {
      wsRef.current = new TradingWebSocket(analysisId);

      wsRef.current.connect(
        (data: any) => {
          // 处理 WebSocket 消息
          switch (data.type) {
            case 'status_update':
              setAnalysisState(data.payload);
              break;
            case 'agent_update':
              setAgentStatuses((prev) => {
                const updated = [...prev];
                const index = updated.findIndex((agent) => agent.id === data.payload.id);
                if (index >= 0) {
                  updated[index] = { ...updated[index], ...data.payload };
                } else {
                  updated.push(data.payload);
                }
                return updated;
              });
              break;
            case 'new_report':
              setReports((prev) => [...prev, data.payload]);
              break;
            case 'final_decision':
              setFinalDecision(data.payload);
              setIsAnalyzing(false);
              toast.success('分析完成！');
              break;
            case 'error':
              toast.error(`分析错误: ${data.payload.message}`);
              setIsAnalyzing(false);
              break;
            default:
              console.log('Unknown WebSocket message type:', data.type);
          }
        },
        (error: any) => {
          console.error('WebSocket error:', error);
          toast.error('实时连接中断，将使用轮询模式');
        }
      );

      return () => {
        if (wsRef.current) {
          wsRef.current.disconnect();
          wsRef.current = null;
        }
      };
    }
  }, [analysisId, isAnalyzing]);

  // 更新状态数据
  useEffect(() => {
    if (statusData?.success) {
      setAnalysisState(statusData.data);
      if (statusData.data.isComplete) {
        setIsAnalyzing(false);
      }
    }
  }, [statusData]);

  useEffect(() => {
    if (agentsData?.success) {
      setAgentStatuses(agentsData.data);
    }
  }, [agentsData]);

  useEffect(() => {
    if (reportsData?.success) {
      setReports(reportsData.data);
    }
  }, [reportsData]);

  useEffect(() => {
    if (decisionData?.success) {
      setFinalDecision(decisionData.data);
    }
  }, [decisionData]);

  // 开始分析
  const startAnalysis = useCallback(() => {
    if (!isAnalyzing) {
      startAnalysisMutation.mutate(config);
    }
  }, [config, isAnalyzing]); // 移除 startAnalysisMutation 依赖，避免不必要的重新创建

  // 停止分析
  const stopAnalysis = useCallback(() => {
    if (analysisId && isAnalyzing) {
      stopAnalysisMutation.mutate(analysisId);
    }
  }, [analysisId, isAnalyzing]); // 移除 stopAnalysisMutation 依赖，避免不必要的重新创建

  // 重新开始分析
  const restartAnalysis = useCallback(() => {
    // 重置状态
    setAnalysisId(null);
    setIsAnalyzing(false);
    setAnalysisState({
      currentStage: 'initialization',
      progress: 0,
      isComplete: false,
    });
    setAgentStatuses([]);
    setReports([]);
    setFinalDecision(null);

    // 清除查询缓存
    queryClient.removeQueries({ queryKey: ['analysisStatus'] });
    queryClient.removeQueries({ queryKey: ['agentStatuses'] });
    queryClient.removeQueries({ queryKey: ['reports'] });
    queryClient.removeQueries({ queryKey: ['tradingDecision'] });

    // 开始新的分析
    setTimeout(() => {
      startAnalysisMutation.mutate(config);
    }, 100);
  }, [queryClient, config]); // 直接使用 mutation 和 config，避免依赖 startAnalysis

  return {
    analysisId,
    isAnalyzing,
    analysisState,
    agentStatuses,
    reports,
    finalDecision,
    startAnalysis,
    stopAnalysis,
    restartAnalysis,
    isStarting: startAnalysisMutation.isPending,
    isStopping: stopAnalysisMutation.isPending,
  };
}
