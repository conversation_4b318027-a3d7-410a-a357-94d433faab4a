#!/usr/bin/env python3
"""
AKShare 连接调试脚本
帮助诊断连接问题
"""

import requests
import socket
import subprocess
import sys
import json
from urllib.parse import urlparse

def check_port(host, port):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"端口检查错误: {e}")
        return False

def check_process_on_port(port):
    """检查指定端口上运行的进程"""
    try:
        if sys.platform == "win32":
            # Windows
            result = subprocess.run(
                ["netstat", "-ano"], 
                capture_output=True, 
                text=True
            )
            lines = result.stdout.split('\n')
            for line in lines:
                if f":{port}" in line and "LISTENING" in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        print(f"端口 {port} 被进程 PID {pid} 占用")
                        return True
        else:
            # Linux/Mac
            result = subprocess.run(
                ["lsof", "-i", f":{port}"], 
                capture_output=True, 
                text=True
            )
            if result.stdout:
                print(f"端口 {port} 使用情况:")
                print(result.stdout)
                return True
    except Exception as e:
        print(f"进程检查错误: {e}")
    
    return False

def test_url_components(url):
    """测试URL的各个组件"""
    print(f"测试URL: {url}")
    parsed = urlparse(url)
    
    print(f"  协议: {parsed.scheme}")
    print(f"  主机: {parsed.hostname}")
    print(f"  端口: {parsed.port}")
    print(f"  路径: {parsed.path}")
    
    # 检查主机解析
    try:
        import socket
        ip = socket.gethostbyname(parsed.hostname)
        print(f"  IP地址: {ip}")
    except Exception as e:
        print(f"  DNS解析失败: {e}")
        return False
    
    # 检查端口连通性
    port_open = check_port(parsed.hostname, parsed.port or 80)
    print(f"  端口开放: {port_open}")
    
    return port_open

def test_akshare_service():
    """测试 AKShare 服务"""
    base_urls = [
        "http://localhost:5000",
        "http://127.0.0.1:5000",
        "http://0.0.0.0:5000"
    ]
    
    print("AKShare 服务连接诊断")
    print("=" * 50)
    
    # 1. 检查端口占用
    print("1. 检查端口 5000 占用情况:")
    port_in_use = check_process_on_port(5000)
    if not port_in_use:
        print("❌ 端口 5000 没有进程监听")
        print("   请启动 AKShare 服务:")
        print("   cd backend/akshare-service")
        print("   python start_service.py")
        return
    
    print()
    
    # 2. 测试不同的URL
    for base_url in base_urls:
        print(f"2. 测试 {base_url}:")
        
        # 测试URL组件
        if not test_url_components(base_url):
            print("   ❌ URL连通性测试失败")
            continue
        
        # 测试根路径
        try:
            response = requests.get(f"{base_url}/", timeout=5)
            print(f"   根路径 (/): {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   服务: {data.get('service', 'unknown')}")
                print(f"   版本: {data.get('version', 'unknown')}")
        except Exception as e:
            print(f"   根路径测试失败: {e}")
        
        # 测试健康检查
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            print(f"   健康检查 (/health): {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   状态: {data.get('status', 'unknown')}")
        except Exception as e:
            print(f"   健康检查失败: {e}")
        
        # 测试API文档
        try:
            response = requests.get(f"{base_url}/docs", timeout=5)
            print(f"   API文档 (/docs): {response.status_code}")
        except Exception as e:
            print(f"   API文档测试失败: {e}")
        
        print()

def test_frontend_config():
    """测试前端配置"""
    print("3. 前端配置检查:")
    
    # 检查环境变量
    import os
    backend_url = os.environ.get('BACK_END_URL', 'http://localhost:5000')
    print(f"   BACK_END_URL 环境变量: {backend_url}")
    
    # 测试前端适配器
    try:
        # 模拟前端适配器的请求
        response = requests.get(f"{backend_url}/health", timeout=5)
        print(f"   前端适配器连接测试: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 前端可以连接到后端")
        else:
            print("   ❌ 前端连接后端失败")
    except Exception as e:
        print(f"   ❌ 前端连接测试失败: {e}")

def main():
    """主函数"""
    print("🔍 AKShare 服务连接诊断工具")
    print("=" * 50)
    
    test_akshare_service()
    test_frontend_config()
    
    print("\n📋 诊断完成!")
    print("\n💡 常见解决方案:")
    print("1. 确保 AKShare 服务已启动: python backend/akshare-service/start_service.py")
    print("2. 检查端口冲突: 确保端口 5000 没有被其他服务占用")
    print("3. 检查防火墙设置: 确保端口 5000 允许访问")
    print("4. 检查环境变量: BACK_END_URL 是否正确设置")

if __name__ == "__main__":
    main()