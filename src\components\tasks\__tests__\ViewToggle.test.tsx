import { render, screen, fireEvent } from '@testing-library/react';
import { ViewToggle, ViewType } from '../ViewToggle';

describe('ViewToggle', () => {
  const mockOnViewChange = jest.fn();

  beforeEach(() => {
    mockOnViewChange.mockClear();
  });

  it('renders both view options', () => {
    render(<ViewToggle currentView="grid" onViewChange={mockOnViewChange} />);
    
    expect(screen.getByText('卡片')).toBeInTheDocument();
    expect(screen.getByText('表格')).toBeInTheDocument();
  });

  it('highlights the current view', () => {
    render(<ViewToggle currentView="grid" onViewChange={mockOnViewChange} />);
    
    const gridButton = screen.getByText('卡片').closest('button');
    const tableButton = screen.getByText('表格').closest('button');
    
    expect(gridButton).toHaveClass('bg-blue-600');
    expect(tableButton).not.toHaveClass('bg-blue-600');
  });

  it('calls onViewChange when clicking different view', () => {
    render(<ViewToggle currentView="grid" onViewChange={mockOnViewChange} />);
    
    const tableButton = screen.getByText('表格');
    fireEvent.click(tableButton);
    
    expect(mockOnViewChange).toHaveBeenCalledWith('table');
  });

  it('has proper accessibility attributes', () => {
    render(<ViewToggle currentView="grid" onViewChange={mockOnViewChange} />);
    
    const gridButton = screen.getByLabelText('卡片视图');
    const tableButton = screen.getByLabelText('表格视图');
    
    expect(gridButton).toBeInTheDocument();
    expect(tableButton).toBeInTheDocument();
  });
});