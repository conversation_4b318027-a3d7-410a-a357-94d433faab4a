'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { TradingDecision as TradingDecisionType }   from '@/lib/services/analysis-service';
import {
    ArrowDownIcon,
    ArrowUpIcon,
    ClockIcon,
    CurrencyDollarIcon,
    ExclamationTriangleIcon,
    MinusIcon,
    ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

interface TradingDecisionProps {
  decision: TradingDecisionType | null;
  isComplete: boolean;
  ticker: string;
}

export function TradingDecision({ decision, isComplete, ticker }: TradingDecisionProps) {
  if (!isComplete) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-slate-600 dark:text-slate-400">分析进行中，交易决策即将生成...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!decision) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <p className="text-slate-600 dark:text-slate-400">暂无交易决策数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'buy':
        return <ArrowUpIcon className="h-8 w-8 text-green-600" />;
      case 'sell':
        return <ArrowDownIcon className="h-8 w-8 text-red-600" />;
      default:
        return <MinusIcon className="h-8 w-8 text-slate-600" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'buy':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'sell':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-slate-600 bg-slate-100 dark:bg-slate-800';
    }
  };

  const getActionText = (action: string) => {
    switch (action) {
      case 'buy':
        return '买入';
      case 'sell':
        return '卖出';
      default:
        return '持有';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'high':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-slate-600 bg-slate-100 dark:bg-slate-800';
    }
  };

  const getRiskText = (risk: string) => {
    switch (risk) {
      case 'low':
        return '低风险';
      case 'medium':
        return '中等风险';
      case 'high':
        return '高风险';
      default:
        return '未知';
    }
  };

  return (
    <div className="space-y-6">
      {/* 决策概览 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>交易决策 - {ticker}</span>
              <div className="flex items-center space-x-2 text-sm text-slate-500">
                <span>{new Date(decision.timestamp).toLocaleString()}</span>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 交易行动 */}
              <div className="text-center">
                <div
                  className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${getActionColor(
                    decision.action
                  )} mb-4`}
                >
                  {getActionIcon(decision.action)}
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                  {getActionText(decision.action)}
                </h3>
                <p className="text-slate-600 dark:text-slate-400">推荐行动</p>
                <div className="mt-2">
                  <span
                    className={`text-xs px-2 py-1 rounded-full ${
                      decision.action === 'buy'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : decision.action === 'sell'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                    }`}
                  >
                    {decision.action === 'buy'
                      ? '看涨'
                      : decision.action === 'sell'
                      ? '看跌'
                      : '观望'}
                  </span>
                </div>
              </div>

              {/* 信心度 */}
              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      className="text-slate-200 dark:text-slate-700"
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path
                      className={`${
                        decision.confidence >= 80
                          ? 'text-green-600'
                          : decision.confidence >= 60
                          ? 'text-blue-600'
                          : decision.confidence >= 40
                          ? 'text-yellow-600'
                          : 'text-red-600'
                      }`}
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      strokeDasharray={`${decision.confidence}, 100`}
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xl font-bold text-slate-900 dark:text-white">
                      {decision.confidence}%
                    </span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">信心度</h3>
                <p className="text-slate-600 dark:text-slate-400">决策可信度</p>
                <div className="mt-2">
                  <span
                    className={`text-xs px-2 py-1 rounded-full ${
                      decision.confidence >= 80
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : decision.confidence >= 60
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                        : decision.confidence >= 40
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}
                  >
                    {decision.confidence >= 80
                      ? '高信心'
                      : decision.confidence >= 60
                      ? '中等信心'
                      : decision.confidence >= 40
                      ? '低信心'
                      : '极低信心'}
                  </span>
                </div>
              </div>

              {/* 风险等级 */}
              <div className="text-center">
                <div
                  className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${getRiskColor(
                    decision.riskLevel
                  )} mb-4`}
                >
                  <ShieldCheckIcon className="h-8 w-8" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                  {getRiskText(decision.riskLevel)}
                </h3>
                <p className="text-slate-600 dark:text-slate-400">风险评估</p>
                <div className="mt-2">
                  <span
                    className={`text-xs px-2 py-1 rounded-full ${getRiskColor(decision.riskLevel)}`}
                  >
                    {decision.riskLevel === 'low'
                      ? '保守型'
                      : decision.riskLevel === 'medium'
                      ? '平衡型'
                      : '激进型'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 详细参数 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>交易参数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {decision.targetPrice && (
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium text-green-800 dark:text-green-200">
                      目标价格
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                    ${decision.targetPrice.toFixed(2)}
                  </div>
                  <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                    预期收益目标
                  </div>
                </div>
              )}

              {decision.stopLoss && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <ShieldCheckIcon className="h-5 w-5 text-red-600" />
                    <span className="text-sm font-medium text-red-800 dark:text-red-200">
                      止损价格
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                    ${decision.stopLoss.toFixed(2)}
                  </div>
                  <div className="text-xs text-red-600 dark:text-red-400 mt-1">风险控制线</div>
                </div>
              )}

              {decision.positionSize && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      仓位大小
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {decision.positionSize}%
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">建议配置比例</div>
                </div>
              )}

              {decision.timeHorizon && (
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <ClockIcon className="h-5 w-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                      时间周期
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {decision.timeHorizon === 'short_term'
                      ? '短期'
                      : decision.timeHorizon === 'medium_term'
                      ? '中期'
                      : decision.timeHorizon === 'long_term'
                      ? '长期'
                      : decision.timeHorizon}
                  </div>
                  <div className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                    持有建议期限
                  </div>
                </div>
              )}
            </div>

            {/* 风险收益比 */}
            {decision.targetPrice && decision.stopLoss && (
              <div className="mt-6 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                  风险收益分析
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">
                      {(((decision.targetPrice - 100) / 100) * 100).toFixed(1)}%
                    </div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">预期收益</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-red-600">
                      {(((100 - decision.stopLoss) / 100) * 100).toFixed(1)}%
                    </div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">最大风险</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">
                      {((decision.targetPrice - 100) / (100 - decision.stopLoss)).toFixed(2)}:1
                    </div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">风险收益比</div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* 决策分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 决策理由 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>💡</span>
                <span>决策理由</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-slate dark:prose-invert max-w-none">
                <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                  {decision.reasoning}
                </p>
              </div>

              {/* 关键因素 */}
              <div className="mt-6 space-y-3">
                <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  关键决策因素:
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-slate-600 dark:text-slate-400">基本面分析支持</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-slate-600 dark:text-slate-400">技术指标确认</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-slate-600 dark:text-slate-400">市场情绪积极</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-slate-600 dark:text-slate-400">风险可控</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 风险评估详情 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>⚠️</span>
                <span>风险评估</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 风险等级指示器 */}
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    风险等级:
                  </span>
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <div
                        key={level}
                        className={`w-4 h-4 rounded-full ${
                          level <=
                          (decision.riskLevel === 'low'
                            ? 2
                            : decision.riskLevel === 'medium'
                            ? 3
                            : 5)
                            ? decision.riskLevel === 'low'
                              ? 'bg-green-500'
                              : decision.riskLevel === 'medium'
                              ? 'bg-yellow-500'
                              : 'bg-red-500'
                            : 'bg-slate-200 dark:bg-slate-700'
                        }`}
                      ></div>
                    ))}
                  </div>
                </div>

                {/* 风险因素 */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    主要风险因素:
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                      <span className="text-sm text-slate-600 dark:text-slate-400">市场风险</span>
                      <span
                        className={`text-xs px-2 py-1 rounded ${
                          decision.riskLevel === 'low'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : decision.riskLevel === 'medium'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        }`}
                      >
                        {decision.riskLevel === 'low'
                          ? '低'
                          : decision.riskLevel === 'medium'
                          ? '中'
                          : '高'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                      <span className="text-sm text-slate-600 dark:text-slate-400">流动性风险</span>
                      <span className="text-xs px-2 py-1 rounded bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        低
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                      <span className="text-sm text-slate-600 dark:text-slate-400">信用风险</span>
                      <span className="text-xs px-2 py-1 rounded bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        低
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                      <span className="text-sm text-slate-600 dark:text-slate-400">操作风险</span>
                      <span className="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                        中
                      </span>
                    </div>
                  </div>
                </div>

                {/* 风险缓解措施 */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    风险缓解措施:
                  </h4>
                  <div className="space-y-1 text-sm text-slate-600 dark:text-slate-400">
                    <div className="flex items-center space-x-2">
                      <span>•</span>
                      <span>设置合理的止损位</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span>•</span>
                      <span>分批建仓降低风险</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span>•</span>
                      <span>密切关注市场变化</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span>•</span>
                      <span>定期评估和调整策略</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* 免责声明 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="text-center"
      >
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            ⚠️ <strong>免责声明:</strong> 此交易决策仅供参考，不构成投资建议。
            投资有风险，决策需谨慎。请根据自身情况和风险承受能力做出投资决定。
          </p>
        </div>
      </motion.div>
    </div>
  );
}
