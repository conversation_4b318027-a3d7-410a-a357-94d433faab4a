# API 客户端迁移总结

本文档总结了将前端代码从直接使用 `fetch` 调用迁移到使用统一 API 客户端的工作。

## 🎯 迁移目标

将所有前端组件和钩子中的直接 `fetch` 调用替换为使用 `src/lib/api.ts` 中的 API 客户端，以确保：

1. **认证信息传递**: 所有请求都会自动携带认证 cookies 和 headers
2. **统一错误处理**: 使用统一的错误处理机制
3. **类型安全**: 利用 TypeScript 类型检查
4. **代码一致性**: 统一的 API 调用方式

## 📋 修复的文件

### 1. 分析页面 (`src/app/[locale]/analysis/[id]/page.tsx`)

**修复前**:
```typescript
const response = await fetch(`/api/analysis/${workflowId}/status`);
const result = await response.json();
```

**修复后**:
```typescript
const result = await analysisApi.getAnalysisStatus(workflowId);
```

**修复的功能**:
- ✅ 获取分析状态
- ✅ 停止分析

### 2. 综合报告查看器 (`src/components/dashboard/ComprehensiveReportViewer.tsx`)

**修复前**:
```typescript
const response = await fetch(`/api/analysis/${workflowId}/comprehensive-report`);
```

**修复后**:
```typescript
const result = await analysisApi.getComprehensiveReport(workflowId);
```

**修复的功能**:
- ✅ 加载综合报告
- ✅ 导出报告

### 3. 分析比较选择器 (`src/components/analysis/AnalysisComparisonSelector.tsx`)

**修复前**:
```typescript
const response = await fetch(`/api/analysis/history?${params}`);
```

**修复后**:
```typescript
const result = await analysisApi.getAnalysisHistory(params);
```

**修复的功能**:
- ✅ 获取分析历史

### 4. 分析页面组件 (`src/app/[locale]/analysis/[id]/AnalysisPage.tsx`)

**修复前**:
```typescript
const statusResponse = await fetch(`/api/langgraph/analysis/${analysisId}/status`);
const agentsResponse = await fetch(`/api/langgraph/analysis/${analysisId}/agents`);
// ... 更多 fetch 调用
```

**修复后**:
```typescript
const statusResult = await analysisApi.getAnalysisStatus(analysisId);
const agentsResult = await analysisApi.getAgentStatuses(analysisId);
// ... 使用 API 客户端
```

**修复的功能**:
- ✅ 获取分析状态
- ✅ 获取代理状态
- ✅ 获取分析报告
- ✅ 获取交易决策
- ✅ 停止分析
- ✅ 重启分析

### 5. 风险数据存储钩子 (`src/hooks/useRiskDataStorage.ts`)

**修复前**:
```typescript
const response = await fetch('/api/risk-data', { method: 'POST', ... });
```

**修复后**:
```typescript
const result = await riskDataApi.saveRiskAssessment(data);
```

**修复的功能**:
- ✅ 保存风险评估
- ✅ 获取风险评估
- ✅ 获取风险评估历史
- ✅ 获取风险指标
- ✅ 比较风险评估

## 🆕 新增的 API 端点

为了支持所有功能，我们创建了以下新的 API 端点：

### 分析相关端点
- `POST /api/analysis/{workflowId}/stop` - 停止分析
- `GET /api/analysis/{workflowId}/agents` - 获取代理状态
- `GET /api/analysis/{workflowId}/reports` - 获取分析报告
- `GET /api/analysis/{workflowId}/decision` - 获取交易决策
- `GET /api/analysis/{workflowId}/comprehensive-report` - 获取综合报告
- `GET /api/analysis/{workflowId}/export` - 导出报告
- `POST /api/analysis/{workflowId}/restart` - 重启分析

### API 客户端方法
在 `src/lib/api.ts` 中新增了以下 API 对象：

#### `analysisApi`
```typescript
export const analysisApi = {
  getAnalysisStatus: (workflowId: string) => Promise<ApiResponse<any>>,
  stopAnalysis: (workflowId: string) => Promise<ApiResponse<void>>,
  getAgentStatuses: (workflowId: string) => Promise<ApiResponse<any>>,
  getReports: (workflowId: string) => Promise<ApiResponse<any>>,
  getTradingDecision: (workflowId: string) => Promise<ApiResponse<any>>,
  startAnalysis: (taskId: string) => Promise<ApiResponse<any>>,
  getAnalysisHistory: (params) => Promise<ApiResponse<any>>,
  getAnalysisStats: () => Promise<ApiResponse<any>>,
  compareAnalyses: (workflowIds: string[]) => Promise<ApiResponse<any>>,
  getRecentAnalyses: (limit?: number) => Promise<ApiResponse<any>>,
  getComprehensiveReport: (workflowId: string) => Promise<ApiResponse<any>>,
  exportReport: (workflowId: string, format) => Promise<Blob>,
  restartAnalysis: (workflowId: string) => Promise<ApiResponse<any>>,
}
```

#### `riskDataApi`
```typescript
export const riskDataApi = {
  saveRiskAssessment: (data: any) => Promise<ApiResponse<any>>,
  getRiskAssessment: (workflowId: string) => Promise<ApiResponse<any>>,
  getRiskAssessmentHistory: (params) => Promise<ApiResponse<any>>,
  getRiskMetrics: (params) => Promise<ApiResponse<any>>,
  compareRiskAssessments: (workflowIds: string[]) => Promise<ApiResponse<any>>,
}
```

## 🔧 API 客户端特性

### 自动认证
所有 API 调用都会自动包含：
- Session cookies
- 认证 headers
- CSRF 保护

### 统一错误处理
```typescript
// 响应拦截器自动处理错误
api.interceptors.response.use(
  (response) => {
    // 成功响应处理
    return {
      success: true,
      data: response.data,
      message: 'Request successful',
    };
  },
  (error) => {
    // 错误响应处理
    return Promise.resolve({
      success: false,
      data: null,
      message: error.message,
      code: error.response?.status,
    });
  }
);
```

### 统一响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}
```

## ✅ 验证结果

### 修复前的问题
- ❌ 直接 fetch 调用不携带认证信息
- ❌ 错误处理不一致
- ❌ 代码重复
- ❌ 类型安全性差

### 修复后的改进
- ✅ 所有请求自动携带认证信息
- ✅ 统一的错误处理机制
- ✅ 代码复用和一致性
- ✅ 完整的 TypeScript 类型支持
- ✅ 更好的可维护性

## 🧪 测试建议

1. **认证测试**: 验证所有 API 调用都正确携带认证信息
2. **错误处理测试**: 测试各种错误情况的处理
3. **功能测试**: 确保所有修复的功能正常工作
4. **性能测试**: 验证 API 客户端不会影响性能

## 📚 使用指南

### 新组件开发
在开发新组件时，请使用 API 客户端而不是直接的 fetch 调用：

```typescript
// ✅ 推荐
import { analysisApi } from '@/lib/api';

const result = await analysisApi.getAnalysisStatus(workflowId);
if (result.success) {
  setData(result.data);
} else {
  setError(result.message);
}

// ❌ 不推荐
const response = await fetch(`/api/analysis/${workflowId}/status`);
const result = await response.json();
```

### 错误处理
API 客户端返回的响应总是 resolved，需要检查 `success` 字段：

```typescript
const result = await analysisApi.getAnalysisStatus(workflowId);
if (!result.success) {
  // 处理错误
  console.error(result.message);
  return;
}
// 处理成功响应
console.log(result.data);
```

## 🔮 未来改进

1. **请求缓存**: 实现智能请求缓存机制
2. **重试机制**: 添加自动重试失败的请求
3. **请求去重**: 避免重复的并发请求
4. **离线支持**: 添加离线模式支持
5. **性能监控**: 添加 API 性能监控和分析

## 📝 注意事项

1. **导出功能**: 导出报告功能仍使用 fetch，因为需要处理 Blob 响应
2. **向后兼容**: 保持了 `tradingApi` 对象以确保向后兼容性
3. **类型定义**: 某些 API 响应类型可能需要进一步完善
4. **错误码**: 建议标准化 API 错误码以便更好的错误处理

这次迁移大大提高了代码的一致性、可维护性和安全性，为后续的功能开发奠定了良好的基础。