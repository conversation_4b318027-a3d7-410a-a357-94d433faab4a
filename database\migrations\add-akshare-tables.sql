-- AKShare 数据缓存表迁移脚本
-- 版本: 4.1
-- 创建时间: 2025-01-18
-- 说明: 为现有数据库添加 AKShare 数据缓存和工作流绑定功能
-- 注意: 此脚本适用于已有表的情况，只添加缺失的字段和表

USE trading_analysis;

-- 检查并添加缺失的字段和表

-- 1. 检查 stock_daily_data 表是否存在 workflow_id 字段，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'trading_analysis' 
    AND TABLE_NAME = 'stock_daily_data' 
    AND COLUMN_NAME = 'workflow_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE stock_daily_data ADD COLUMN workflow_id VARCHAR(36) NULL COMMENT ''关联工作流ID'' AFTER ticker, ADD INDEX idx_workflow_id (workflow_id)',
    'SELECT ''stock_daily_data.workflow_id 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 检查 financial_news 表是否存在 workflow_id 字段，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'trading_analysis' 
    AND TABLE_NAME = 'financial_news' 
    AND COLUMN_NAME = 'workflow_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE financial_news ADD COLUMN workflow_id VARCHAR(36) NULL COMMENT ''关联工作流ID'' AFTER news_id, ADD INDEX idx_workflow_id (workflow_id)',
    'SELECT ''financial_news.workflow_id 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 检查 stock_fundamental_data 表是否存在 workflow_id 字段，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'trading_analysis' 
    AND TABLE_NAME = 'stock_fundamental_data' 
    AND COLUMN_NAME = 'workflow_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE stock_fundamental_data ADD COLUMN workflow_id VARCHAR(36) NULL COMMENT ''关联工作流ID'' AFTER symbol, ADD INDEX idx_workflow_id (workflow_id)',
    'SELECT ''stock_fundamental_data.workflow_id 字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 数据获取日志表 (与工作流关联)
CREATE TABLE IF NOT EXISTS data_fetch_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(36) UNIQUE NOT NULL COMMENT '日志唯一标识',
    workflow_id VARCHAR(36) COMMENT '关联工作流ID',
    data_type VARCHAR(50) NOT NULL COMMENT '数据类型',
    symbol VARCHAR(20) NOT NULL COMMENT '股票代码',
    fetch_params JSON COMMENT '获取参数',
    data_count INT DEFAULT 0 COMMENT '数据条数',
    source VARCHAR(50) DEFAULT 'akshare' COMMENT '数据源',
    status ENUM('success', 'failed', 'partial') DEFAULT 'success' COMMENT '获取状态',
    error_message TEXT COMMENT '错误信息',
    execution_time_ms INT COMMENT '执行耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE SET NULL,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_data_type (data_type),
    INDEX idx_symbol (symbol),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据获取日志表';

-- 创建视图：工作流数据获取统计
CREATE OR REPLACE VIEW workflow_data_stats AS
SELECT 
    w.workflow_id,
    w.ticker,
    w.title,
    w.status as workflow_status,
    COUNT(dfl.id) as total_data_fetches,
    SUM(dfl.data_count) as total_data_records,
    COUNT(CASE WHEN dfl.status = 'success' THEN 1 END) as successful_fetches,
    COUNT(CASE WHEN dfl.status = 'failed' THEN 1 END) as failed_fetches,
    AVG(dfl.execution_time_ms) as avg_execution_time_ms,
    GROUP_CONCAT(DISTINCT dfl.data_type) as data_types_fetched,
    MAX(dfl.created_at) as last_fetch_time
FROM workflows w
LEFT JOIN data_fetch_logs dfl ON w.workflow_id = dfl.workflow_id
GROUP BY w.workflow_id, w.ticker, w.title, w.status;

-- 存储过程：记录数据获取日志
DROP PROCEDURE IF EXISTS LogDataFetch;
DELIMITER //
CREATE PROCEDURE LogDataFetch(
    IN p_workflow_id VARCHAR(36),
    IN p_data_type VARCHAR(50),
    IN p_symbol VARCHAR(20),
    IN p_fetch_params JSON,
    IN p_data_count INT,
    IN p_source VARCHAR(50),
    IN p_status VARCHAR(20),
    IN p_error_message TEXT,
    IN p_execution_time_ms INT
)
BEGIN
    DECLARE log_uuid VARCHAR(36);
    SET log_uuid = UUID();
    
    INSERT INTO data_fetch_logs (
        log_id, workflow_id, data_type, symbol, fetch_params, 
        data_count, source, status, error_message, execution_time_ms
    ) VALUES (
        log_uuid, p_workflow_id, p_data_type, p_symbol, p_fetch_params,
        p_data_count, p_source, p_status, p_error_message, p_execution_time_ms
    );
END //
DELIMITER ;

-- 存储过程：获取工作流的数据获取统计
DROP PROCEDURE IF EXISTS GetWorkflowDataStats;
DELIMITER //
CREATE PROCEDURE GetWorkflowDataStats(
    IN p_workflow_id VARCHAR(36)
)
BEGIN
    SELECT 
        data_type,
        COUNT(*) as fetch_count,
        SUM(data_count) as total_records,
        AVG(execution_time_ms) as avg_execution_time,
        MAX(created_at) as last_fetch_time,
        COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
    FROM data_fetch_logs 
    WHERE workflow_id = p_workflow_id 
    GROUP BY data_type
    ORDER BY last_fetch_time DESC;
END //
DELIMITER ;

SELECT 'AKShare 数据缓存表迁移完成!' as status;