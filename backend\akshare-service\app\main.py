"""
AKShare 后端服务主文件
提供金融数据获取的RESTful API接口
支持数据库缓存以提高性能和减少API调用

这个服务将akshare的数据获取功能封装为API，
供前端通过HTTP请求调用，而不是在前端直接运行Python代码。
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import akshare as ak
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import logging
from pydantic import BaseModel
import uvicorn
import os
import mysql.connector
from mysql.connector import Error
import hashlib
import re

# --- 配置 ---
# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置 (从环境变量获取)
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", 3306)
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "password")
DB_NAME = os.getenv("DB_NAME", "trading_analysis")


# --- 数据库连接 ---
def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
        )
        if connection.is_connected():
            return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None


# --- FastAPI 应用实例 ---
app = FastAPI(
    title="AKShare 数据服务",
    description="提供中国金融市场数据的API服务，并支持数据库缓存。",
    version="1.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# --- Pydantic 请求模型 ---
class StockHistoryRequest(BaseModel):
    """股票历史数据请求模型"""

    symbol: str
    period: Optional[str] = "daily"
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    adjust: Optional[str] = ""
    use_cache: Optional[bool] = True
    time_period: Optional[str] = None
    workflow_id: Optional[str] = None  # 工作流ID


class StockNewsRequest(BaseModel):
    """股票新闻请求模型"""

    symbol: str
    limit: int = 20
    use_cache: bool = True  # 新增：是否使用缓存
    workflow_id: Optional[str] = None  # 工作流ID


class StockFundamentalRequest(BaseModel):
    """股票基本面数据请求模型"""
    
    symbol: str
    indicator: str = "all"  # 指标类型: all, financial_analysis, balance_sheet, profit_sheet, cash_flow_sheet
    period_type: str = "按报告期"  # 报告期类型: 按报告期, 按年度, 按单季度
    time_period: Optional[str] = None  # 时间周期，如 '1y', '2y', '3m' 等
    use_cache: bool = True
    workflow_id: Optional[str] = None  # 工作流ID


class StockRealtimeRequest(BaseModel):
    """股票实时数据请求模型"""
    
    symbol: str
    workflow_id: Optional[str] = None  # 工作流ID


class StockTechnicalRequest(BaseModel):
    """股票技术指标请求模型"""
    
    symbol: str
    indicator: Optional[str] = None
    period: Optional[str] = None
    workflow_id: Optional[str] = None  # 工作流ID


class GeneralNewsRequest(BaseModel):
    """事实新闻请求模型"""

    limit: int = 20
    use_cache: bool = True
    date: Optional[str] = None  # 可选的日期参数，格式：YYYYMMDD
    workflow_id: Optional[str] = None  # 工作流ID


# --- 工具函数 ---
def safe_json_convert(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """安全地将DataFrame转换为JSON格式"""
    try:
        df = df.copy()
        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                df[col] = df[col].dt.strftime("%Y-%m-%d %H:%M:%S")

        if isinstance(df.index, pd.DatetimeIndex):
            df.index = df.index.strftime("%Y-%m-%d")

        return df.fillna("").to_dict("records")
    except Exception as e:
        logger.error(f"JSON转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据转换失败: {str(e)}")


def parse_time_period(time_period: str) -> int:
    """
    解析时间周期字符串，返回对应的天数

    支持的格式：
    - 数字 + d (天): 1d, 3d, 30d
    - 数字 + w (周): 1w, 2w, 4w
    - 数字 + m (月): 1m, 3m, 6m, 12m
    - 数字 + y (年): 1y, 2y, 5y

    Args:
        time_period: 时间周期字符串，如 "3d", "2w", "6m", "1y"

    Returns:
        int: 对应的天数

    Raises:
        ValueError: 如果格式不正确
    """
    if not time_period:
        raise ValueError("时间周期不能为空")

    # 使用正则表达式解析格式：数字 + 单位
    pattern = r"^(\d+)([dwmy])$"
    match = re.match(pattern, time_period.lower().strip())

    if not match:
        raise ValueError(
            f"无效的时间周期格式: {time_period}，支持格式: 数字+d/w/m/y (如: 3d, 2w, 6m, 1y)"
        )

    number = int(match.group(1))
    unit = match.group(2)

    # 验证数字范围
    if number <= 0:
        raise ValueError(f"时间周期数字必须大于0: {number}")

    if number > 9999:  # 设置合理的上限
        raise ValueError(f"时间周期数字过大: {number}，最大支持9999")

    # 计算天数
    if unit == "d":  # 天
        days = number
    elif unit == "w":  # 周
        days = number * 7
    elif unit == "m":  # 月 (按30天计算)
        days = number * 30
    elif unit == "y":  # 年 (按365天计算)
        days = number * 365
    else:
        raise ValueError(f"不支持的时间单位: {unit}")

    # 验证计算结果的合理性 (最多支持20年的数据)
    if days > 365 * 20:  # 最多20年
        raise ValueError(f"时间周期过长: {days}天，最多支持20年")

    return days


def generate_news_id(url: str) -> str:
    """根据URL生成唯一的新闻ID"""
    return hashlib.sha256(url.encode("utf-8")).hexdigest()


def log_workflow_event(workflow_id: str, event_type: str, content: str, metadata: Dict[str, Any] = None):
    """记录工作流事件到数据库"""
    if not workflow_id:
        return
    
    db_conn = get_db_connection()
    if db_conn:
        try:
            cursor = db_conn.cursor()
            event_id = hashlib.sha256(f"{workflow_id}_{datetime.now().isoformat()}_{event_type}".encode()).hexdigest()[:16]
            
            insert_query = """
                INSERT INTO workflow_events (event_id, workflow_id, event_type, content, metadata)
                VALUES (%s, %s, %s, %s, %s)
            """
            
            metadata_json = json.dumps(metadata or {}, ensure_ascii=False)
            cursor.execute(insert_query, (event_id, workflow_id, event_type, content, metadata_json))
            db_conn.commit()
            logger.info(f"记录工作流事件: {workflow_id} - {event_type}")
            
        except Error as e:
            logger.error(f"记录工作流事件失败: {e}")
            db_conn.rollback()
        finally:
            if db_conn.is_connected():
                cursor.close()
                db_conn.close()


def bind_data_to_workflow(workflow_id: str, data_type: str, symbol: str, data_count: int, source: str = "akshare", execution_time_ms: int = 0, fetch_params: Dict[str, Any] = None):
    """将数据获取操作绑定到工作流"""
    if not workflow_id:
        return
    
    # 记录到工作流事件
    content = f"获取{data_type}数据: {symbol}, 数据量: {data_count}, 来源: {source}"
    metadata = {
        "data_type": data_type,
        "symbol": symbol,
        "data_count": data_count,
        "source": source,
        "execution_time_ms": execution_time_ms,
        "timestamp": datetime.now().isoformat()
    }
    
    log_workflow_event(workflow_id, "tool_call", content, metadata)
    
    # 记录到数据获取日志表
    db_conn = get_db_connection()
    if db_conn:
        try:
            cursor = db_conn.cursor()
            log_uuid = hashlib.sha256(f"{workflow_id}_{datetime.now().isoformat()}_{data_type}_{symbol}".encode()).hexdigest()[:16]
            
            insert_query = """
                INSERT INTO data_fetch_logs (
                    log_id, workflow_id, data_type, symbol, fetch_params, 
                    data_count, source, status, error_message, execution_time_ms
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, (
                log_uuid,
                workflow_id,
                data_type,
                symbol,
                json.dumps(fetch_params or {}, ensure_ascii=False),
                data_count,
                source,
                'success',
                None,
                execution_time_ms
            ))
            db_conn.commit()
            logger.info(f"记录数据获取日志: {workflow_id} - {data_type} - {symbol}")
            
        except Error as e:
            logger.error(f"记录数据获取日志失败: {e}")
            db_conn.rollback()
        finally:
            if db_conn.is_connected():
                cursor.close()
                db_conn.close()


def log_data_fetch_error(workflow_id: str, data_type: str, symbol: str, error_message: str, fetch_params: Dict[str, Any] = None):
    """记录数据获取错误"""
    if not workflow_id:
        return
    
    db_conn = get_db_connection()
    if db_conn:
        try:
            cursor = db_conn.cursor()
            log_uuid = hashlib.sha256(f"{workflow_id}_{datetime.now().isoformat()}_{data_type}_{symbol}_error".encode()).hexdigest()[:16]
            
            insert_query = """
                INSERT INTO data_fetch_logs (
                    log_id, workflow_id, data_type, symbol, fetch_params, 
                    data_count, source, status, error_message, execution_time_ms
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, (
                log_uuid,
                workflow_id,
                data_type,
                symbol,
                json.dumps(fetch_params or {}, ensure_ascii=False),
                0,
                'akshare',
                'failed',
                error_message,
                0
            ))
            db_conn.commit()
            logger.info(f"记录数据获取错误: {workflow_id} - {data_type} - {symbol}")
            
        except Error as e:
            logger.error(f"记录数据获取错误失败: {e}")
            db_conn.rollback()
        finally:
            if db_conn.is_connected():
                cursor.close()
                db_conn.close()


# --- API 路由 ---
@app.get("/")
async def root():
    return {"service": "AKShare 数据服务", "version": "1.1.0", "status": "运行中"}


@app.post("/api/stock/history")
async def get_stock_history(request: StockHistoryRequest):
    """获取股票历史数据，支持缓存和时间周期"""
    # 记录工作流事件
    if request.workflow_id:
        log_workflow_event(
            request.workflow_id, 
            "tool_call", 
            f"开始获取股票历史数据: {request.symbol}",
            {
                "action": "get_stock_history",
                "symbol": request.symbol,
                "period": request.period,
                "time_period": request.time_period
            }
        )
    
    end_date = datetime.now()
    
    # 默认时间范围为一年
    if not request.time_period and not request.start_date and not request.end_date:
        start_date = end_date - timedelta(days=365)
        start_date_str = start_date.strftime("%Y%m%d")
        end_date_str = end_date.strftime("%Y%m%d")
    elif request.time_period:
        try:
            # 使用动态解析函数计算天数
            days = parse_time_period(request.time_period)
            start_date = end_date - timedelta(days=days)
            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")
            logger.info(
                f"使用时间周期 {request.time_period} ({days}天): {start_date_str} 到 {end_date_str}"
            )
        except ValueError as e:
            # 如果解析失败，使用默认值并记录警告
            logger.warning(f"时间周期解析失败: {str(e)}，使用默认值1年")
            start_date = end_date - timedelta(days=365)
            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")
    else:
        # 使用提供的起止日期
        start_date_str = request.start_date or datetime.strftime(end_date - timedelta(days=365), "%Y%m%d")
        end_date_str = request.end_date or end_date.strftime("%Y%m%d")

    if request.use_cache:
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor(dictionary=True)
                query = """
                    SELECT * FROM stock_daily_data
                    WHERE ticker = %s AND trade_date BETWEEN %s AND %s
                    ORDER BY trade_date ASC
                """
                start_date_sql = datetime.strptime(start_date_str, "%Y%m%d").strftime(
                    "%Y-%m-%d"
                )
                end_date_sql = datetime.strptime(end_date_str, "%Y%m%d").strftime(
                    "%Y-%m-%d"
                )

                cursor.execute(query, (request.symbol, start_date_sql, end_date_sql))
                result = cursor.fetchall()

                if (
                    result
                    and len(result)
                    >= (
                        datetime.strptime(end_date_str, "%Y%m%d")
                        - datetime.strptime(start_date_str, "%Y%m%d")
                    ).days
                    * 0.8
                ):
                    logger.info(f"从缓存获取股票历史数据: {request.symbol}")
                    return {
                        "success": True,
                        "data": result,
                        "count": len(result),
                        "source": "cache",
                    }
            except Error as e:
                logger.error(f"查询缓存失败: {e}")
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

    try:
        logger.info(f"从API获取股票历史数据: {request.symbol}")
        
        # 记录开始时间
        start_time = datetime.now()

        # 自动识别市场并获取数据
        df = ak.stock_zh_a_hist(symbol=request.symbol, period=request.period, adjust=request.adjust)
        
        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="未找到股票数据")

        # 重命名字段以匹配数据库
        df.rename(
            columns={
                "日期": "trade_date",
                "开盘": "open",
                "最高": "high",
                "最低": "low",
                "收盘": "close",
                "成交量": "volume",
                "成交额": "turnover",
                "振幅": "amplitude",
                "涨跌幅": "change_pct",
                "涨跌额": "change_amount",
                "换手率": "turnover_rate",
            },
            inplace=True,
        )
        df["ticker"] = request.symbol
        df["workflow_id"] = request.workflow_id  # 添加工作流ID

        # 存入数据库
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor()
                insert_query = """
                    INSERT INTO stock_daily_data (ticker, workflow_id, trade_date, open, high, low, close, volume, turnover, amplitude, change_pct, change_amount, turnover_rate)
                    VALUES (%(ticker)s, %(workflow_id)s, %(trade_date)s, %(open)s, %(high)s, %(low)s, %(close)s, %(volume)s, %(turnover)s, %(amplitude)s, %(change_pct)s, %(change_amount)s, %(turnover_rate)s)
                    ON DUPLICATE KEY UPDATE
                    workflow_id=VALUES(workflow_id), open=VALUES(open), high=VALUES(high), low=VALUES(low), close=VALUES(close), volume=VALUES(volume), turnover=VALUES(turnover),
                    amplitude=VALUES(amplitude), change_pct=VALUES(change_pct), change_amount=VALUES(change_amount), turnover_rate=VALUES(turnover_rate);
                """
                data_tuples = [row.to_dict() for index, row in df.iterrows()]
                cursor.executemany(insert_query, data_tuples)
                db_conn.commit()
                logger.info(
                    f"成功缓存 {cursor.rowcount} 条股票历史数据: {request.symbol}"
                )
            except Error as e:
                logger.error(f"缓存股票历史数据失败: {e}")
                db_conn.rollback()
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

        data = safe_json_convert(df)
        
        # 计算执行时间
        execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # 绑定数据到工作流
        if request.workflow_id:
            fetch_params = {
                "symbol": request.symbol,
                "period": request.period,
                "start_date": start_date_str,
                "end_date": end_date_str,
                "adjust": request.adjust
            }
            bind_data_to_workflow(
                request.workflow_id, 
                "股票历史数据", 
                request.symbol, 
                len(data), 
                "akshare_api",
                execution_time_ms,
                fetch_params
            )
        
        return {"success": True, "data": data, "count": len(data), "source": "api"}

    except Exception as e:
        # 记录错误事件
        if request.workflow_id:
            log_workflow_event(
                request.workflow_id,
                "error",
                f"获取股票历史数据失败: {str(e)}",
                {"action": "get_stock_history", "symbol": request.symbol, "error": str(e)}
            )
            # 记录数据获取错误
            fetch_params = {
                "symbol": request.symbol,
                "period": request.period,
                "adjust": request.adjust
            }
            log_data_fetch_error(request.workflow_id, "股票历史数据", request.symbol, str(e), fetch_params)
        
        logger.error(f"获取股票历史数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")


@app.post("/api/stock/news")
async def get_stock_news(request: StockNewsRequest):
    """获取股票相关新闻，支持缓存"""
    # 记录工作流事件
    if request.workflow_id:
        log_workflow_event(
            request.workflow_id,
            "tool_call",
            f"开始获取股票新闻: {request.symbol}",
            {
                "action": "get_stock_news",
                "symbol": request.symbol,
                "limit": request.limit
            }
        )
    
    # 注意：akshare的stock_news_em不按股票代码筛选，这里我们获取通用新闻并缓存
    if request.use_cache:
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor(dictionary=True)
                # 查询特定股票的新闻，如果没有则获取通用新闻
                query = """
                    SELECT * FROM financial_news 
                    WHERE JSON_CONTAINS(related_tickers, %s) OR related_tickers LIKE %s
                    ORDER BY publish_time DESC LIMIT %s
                """
                symbol_json = json.dumps([request.symbol])
                symbol_like = f"%{request.symbol}%"
                cursor.execute(query, (symbol_json, symbol_like, request.limit))
                result = cursor.fetchall()
                if result:
                    logger.info(f"从缓存获取新闻数据: {request.symbol}")
                    return {
                        "success": True,
                        "data": result,
                        "count": len(result),
                        "source": "cache",
                    }
            except Error as e:
                logger.error(f"查询新闻缓存失败: {e}")
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

    try:
        logger.info(f"从API获取新闻数据: {request.symbol}")
        
        # 记录开始时间
        start_time = datetime.now()

        # 尝试多种新闻获取方式
        df = None
        error_messages = []

        # 方法1: 尝试使用 stock_news_em (需要传递symbol参数)
        try:
            df = ak.stock_news_em(symbol=request.symbol)
            logger.info("使用 stock_news_em 成功获取数据")
        except Exception as e:
            error_messages.append(f"stock_news_em 失败: {str(e)}")
            logger.warning(f"stock_news_em 失败: {str(e)}")

        # 方法2: 如果方法1失败，尝试使用东方财富股票新闻
        if df is None or df.empty:
            try:
                df = ak.stock_news_em()
                logger.info("使用 stock_news_em (无symbol) 成功获取数据")
            except Exception as e:
                error_messages.append(f"stock_news_em (无symbol) 失败: {str(e)}")
                logger.warning(f"stock_news_em (无symbol) 失败: {str(e)}")

        # 如果所有方法都失败，抛出异常
        if df is None or df.empty:
            error_detail = "所有新闻接口都失败: " + "; ".join(error_messages)
            logger.error(error_detail)
            raise HTTPException(status_code=503, detail=error_detail)

        df = df.head(request.limit)

        # 处理不同的数据结构
        processed_data = []
        for index, row in df.iterrows():
            # 生成唯一ID
            unique_content = str(row.get("标题", row.get("title", ""))) + str(index)
            news_id = hashlib.sha256(unique_content.encode("utf-8")).hexdigest()[:16]

            # 提取数据，处理不同的字段名
            news_item = {
                "news_id": news_id,
                "workflow_id": request.workflow_id,  # 添加工作流ID
                "title": row.get(
                    "标题", row.get("title", row.get("新闻标题", f"新闻 {index}"))
                ),
                "content": row.get("内容", row.get("content", row.get("新闻内容", ""))),
                "source": row.get("来源", row.get("source", "东方财富")),
                "publish_time": str(
                    row.get(
                        "发布时间",
                        row.get(
                            "时间",
                            row.get(
                                "time", datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            ),
                        ),
                    )
                ),
                "url": row.get(
                    "链接", row.get("url",row.get("url", row.get("新闻链接", "")) )
                ),
                "related_tickers": json.dumps([request.symbol]),
            }
            processed_data.append(news_item)

        df = pd.DataFrame(processed_data)
        df["source"] = "东方财富"

        # 存入数据库
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor()
                insert_query = """
                    INSERT INTO financial_news (news_id, workflow_id, source, title, content, publish_time, url, related_tickers)
                    VALUES (%(news_id)s, %(workflow_id)s, %(source)s, %(title)s, %(content)s, %(publish_time)s, %(url)s, %(related_tickers)s)
                    ON DUPLICATE KEY UPDATE
                    workflow_id=VALUES(workflow_id), title=VALUES(title), content=VALUES(content), publish_time=VALUES(publish_time);
                """
                data_tuples = [row.to_dict() for index, row in df.iterrows()]
                cursor.executemany(insert_query, data_tuples)
                db_conn.commit()
                logger.info(f"成功缓存 {cursor.rowcount} 条新闻数据: {request.symbol}")
            except Error as e:
                logger.error(f"缓存新闻数据失败: {e}")
                db_conn.rollback()
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

        data = safe_json_convert(df)
        
        # 计算执行时间
        execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # 绑定数据到工作流
        if request.workflow_id:
            fetch_params = {
                "symbol": request.symbol,
                "limit": request.limit
            }
            bind_data_to_workflow(
                request.workflow_id,
                "股票新闻",
                request.symbol,
                len(data),
                "akshare_api",
                execution_time_ms,
                fetch_params
            )
        
        return {"success": True, "data": data, "count": len(data), "source": "api"}

    except Exception as e:
        # 记录错误事件
        if request.workflow_id:
            log_workflow_event(
                request.workflow_id,
                "error",
                f"获取股票新闻失败: {str(e)}",
                {"action": "get_stock_news", "symbol": request.symbol, "error": str(e)}
            )
            # 记录数据获取错误
            fetch_params = {
                "symbol": request.symbol,
                "limit": request.limit
            }
            log_data_fetch_error(request.workflow_id, "股票新闻", request.symbol, str(e), fetch_params)
        
        logger.error(f"获取新闻失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取新闻失败: {str(e)}")


@app.post("/api/stock/fundamental")
async def get_stock_fundamental(request: StockFundamentalRequest):
    """获取股票基本面数据，支持缓存"""
    # 记录工作流事件
    if request.workflow_id:
        log_workflow_event(
            request.workflow_id,
            "tool_call",
            f"开始获取基本面数据: {request.symbol}",
            {
                "action": "get_stock_fundamental",
                "symbol": request.symbol,
                "indicator": request.indicator,
                "period_type": request.period_type
            }
        )
    
    # 检查缓存
    if request.use_cache:
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor(dictionary=True)
                # 构建缓存查询键
                cache_key = f"{request.symbol}_{request.indicator}_{request.period_type}"
                if request.time_period:
                    cache_key += f"_{request.time_period}"
                
                query = """
                    SELECT data, created_at FROM stock_fundamental_data 
                    WHERE symbol = %s AND indicator = %s AND period_type = %s AND time_period = %s
                    ORDER BY created_at DESC LIMIT 1
                """
                cursor.execute(query, (
                    request.symbol, 
                    request.indicator, 
                    request.period_type, 
                    request.time_period or ""
                ))
                result = cursor.fetchone()
                if result:
                    # 检查缓存是否过期（24小时）
                    created_at = result['created_at']
                    if datetime.now() - created_at < timedelta(hours=24):
                        logger.info(f"从缓存获取基本面数据: {request.symbol}")
                        return {
                            "success": True,
                            "data": json.loads(result['data']),
                            "count": len(json.loads(result['data'])),
                            "source": "cache",
                        }
            except Error as e:
                logger.error(f"查询基本面缓存失败: {e}")
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

    try:
        logger.info(f"获取股票基本面数据: {request.symbol}, 指标类型: {request.indicator}, 报告期类型: {request.period_type}")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 根据指标类型选择不同的akshare接口
        if request.indicator == "all":
            # 获取所有基本面数据
            df = ak.stock_financial_abstract_ths(symbol=request.symbol, indicator=request.period_type)
        elif request.indicator == "financial_analysis":
            # 获取财务分析数据
            df = ak.stock_financial_analysis_indicator_ths(symbol=request.symbol)
        elif request.indicator == "balance_sheet":
            # 获取资产负债表
            if request.period_type == "按年度":
                df = ak.stock_balance_sheet_by_yearly_em(symbol=request.symbol)
            else:
                df = ak.stock_balance_sheet_by_report_em(symbol=request.symbol)
        elif request.indicator == "profit_sheet":
            # 获取利润表
            if request.period_type == "按年度":
                df = ak.stock_profit_sheet_by_yearly_em(symbol=request.symbol)
            else:
                df = ak.stock_profit_sheet_by_report_em(symbol=request.symbol)
        elif request.indicator == "cash_flow_sheet":
            # 获取现金流量表
            if request.period_type == "按年度":
                df = ak.stock_cash_flow_sheet_by_yearly_em(symbol=request.symbol)
            else:
                df = ak.stock_cash_flow_sheet_by_report_em(symbol=request.symbol)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的指标类型: {request.indicator}")
        
        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="未找到基本面数据")
        
        # 处理时间周期筛选
        if request.time_period and not df.empty:
            # 解析时间周期
            try:
                days = parse_time_period(request.time_period)
                # 根据报告期类型和周期筛选数据
                if request.period_type == "按单季度" and days >= 365:
                    # 如果是按单季度且周期至少一年，则取最近的4个季度数据
                    df = df.tail(4)
                elif request.period_type == "按年度" and days >= 365:
                    # 如果是按年度且周期至少一年，则取最近的年份数据
                    years_needed = max(1, days // 365)
                    df = df.tail(years_needed)
                elif days >= 180:  # 至少6个月
                    # 对于其他情况，至少需要2条数据
                    df = df.tail(max(2, len(df)))
            except ValueError as e:
                logger.warning(f"时间周期解析失败: {str(e)}，返回所有数据")
        
        data = safe_json_convert(df)
        
        # 存入数据库缓存
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor()
                insert_query = """
                    INSERT INTO stock_fundamental_data 
                    (symbol, workflow_id, indicator, period_type, time_period, data)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    workflow_id=VALUES(workflow_id), data=VALUES(data), created_at=CURRENT_TIMESTAMP
                """
                cursor.execute(insert_query, (
                    request.symbol,
                    request.workflow_id,
                    request.indicator,
                    request.period_type,
                    request.time_period or "",
                    json.dumps(data, ensure_ascii=False)
                ))
                db_conn.commit()
                logger.info(f"成功缓存基本面数据: {request.symbol}")
            except Error as e:
                logger.error(f"缓存基本面数据失败: {e}")
                db_conn.rollback()
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()
        
        # 计算执行时间
        execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # 绑定数据到工作流
        if request.workflow_id:
            fetch_params = {
                "symbol": request.symbol,
                "indicator": request.indicator,
                "period_type": request.period_type,
                "time_period": request.time_period
            }
            bind_data_to_workflow(
                request.workflow_id,
                "基本面数据",
                request.symbol,
                len(data),
                "akshare_api",
                execution_time_ms,
                fetch_params
            )
        
        return {"success": True, "data": data, "count": len(data), "source": "api"}
        
    except Exception as e:
        # 记录错误事件
        if request.workflow_id:
            log_workflow_event(
                request.workflow_id,
                "error",
                f"获取基本面数据失败: {str(e)}",
                {"action": "get_stock_fundamental", "symbol": request.symbol, "error": str(e)}
            )
            # 记录数据获取错误
            fetch_params = {
                "symbol": request.symbol,
                "indicator": request.indicator,
                "period_type": request.period_type,
                "time_period": request.time_period
            }
            log_data_fetch_error(request.workflow_id, "基本面数据", request.symbol, str(e), fetch_params)
        
        logger.error(f"获取股票基本面数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取基本面数据失败: {str(e)}")


@app.post("/api/stock/realtime")
async def get_stock_realtime(request: StockRealtimeRequest):
    """获取股票实时数据"""
    # 记录工作流事件
    if request.workflow_id:
        log_workflow_event(
            request.workflow_id,
            "tool_call",
            f"开始获取实时数据: {request.symbol}",
            {
                "action": "get_stock_realtime",
                "symbol": request.symbol
            }
        )
    
    try:
        logger.info(f"获取股票实时数据: {request.symbol}")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 使用akshare获取实时数据
        df = ak.stock_zh_a_spot_em()
        
        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="未找到实时数据")
        
        # 筛选指定股票
        symbol_filter = df['代码'] == request.symbol
        if not symbol_filter.any():
            # 如果没找到，尝试不同的代码格式
            symbol_variants = [
                request.symbol,
                request.symbol.upper(),
                request.symbol.lower(),
                request.symbol.zfill(6)  # 补齐到6位
            ]
            
            for variant in symbol_variants:
                symbol_filter = df['代码'] == variant
                if symbol_filter.any():
                    break
        
        if not symbol_filter.any():
            raise HTTPException(status_code=404, detail=f"未找到股票代码: {request.symbol}")
        
        stock_data = df[symbol_filter].iloc[0].to_dict()
        
        # 计算执行时间
        execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # 绑定数据到工作流
        if request.workflow_id:
            fetch_params = {
                "symbol": request.symbol
            }
            bind_data_to_workflow(
                request.workflow_id,
                "实时数据",
                request.symbol,
                1,
                "akshare_api",
                execution_time_ms,
                fetch_params
            )
        
        return {"success": True, "data": stock_data, "count": 1, "source": "api"}
        
    except Exception as e:
        # 记录错误事件
        if request.workflow_id:
            log_workflow_event(
                request.workflow_id,
                "error",
                f"获取实时数据失败: {str(e)}",
                {"action": "get_stock_realtime", "symbol": request.symbol, "error": str(e)}
            )
            # 记录数据获取错误
            fetch_params = {
                "symbol": request.symbol
            }
            log_data_fetch_error(request.workflow_id, "实时数据", request.symbol, str(e), fetch_params)
        
        logger.error(f"获取股票实时数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取实时数据失败: {str(e)}")


@app.post("/api/stock/technical")
async def get_stock_technical(request: StockTechnicalRequest):
    """获取股票技术指标数据"""
    # 记录工作流事件
    if request.workflow_id:
        log_workflow_event(
            request.workflow_id,
            "tool_call",
            f"开始获取技术指标: {request.symbol}",
            {
                "action": "get_stock_technical",
                "symbol": request.symbol,
                "indicator": request.indicator,
                "period": request.period
            }
        )
    
    try:
        logger.info(f"获取技术指标数据: {request.symbol}, 指标: {request.indicator}")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 直接获取技术指标数据，不进行额外处理
        if request.indicator and hasattr(ak, request.indicator):
            # 如果请求的指标在 akshare 中有对应的方法，则直接调用
            df = getattr(ak, request.indicator)(symbol=request.symbol)
        else:
            # 否则使用 stock_technical_indicator 接口
            df = ak.stock_technical_indicator(symbol=request.symbol)
        
        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="未找到技术指标数据")
        
        data = safe_json_convert(df)
        
        # 计算执行时间
        execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # 绑定数据到工作流
        if request.workflow_id:
            fetch_params = {
                "symbol": request.symbol,
                "indicator": request.indicator,
                "period": request.period
            }
            bind_data_to_workflow(
                request.workflow_id,
                "技术指标",
                request.symbol,
                len(data),
                "akshare_api",
                execution_time_ms,
                fetch_params
            )
        
        # 直接返回数据，不做额外处理
        return {"success": True, "data": data, "count": len(data), "source": "api"}
        
    except Exception as e:
        # 记录错误事件
        if request.workflow_id:
            log_workflow_event(
                request.workflow_id,
                "error",
                f"获取技术指标失败: {str(e)}",
                {"action": "get_stock_technical", "symbol": request.symbol, "error": str(e)}
            )
            # 记录数据获取错误
            fetch_params = {
                "symbol": request.symbol,
                "indicator": request.indicator,
                "period": request.period
            }
            log_data_fetch_error(request.workflow_id, "技术指标", request.symbol, str(e), fetch_params)
        
        logger.error(f"获取技术指标数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取技术指标失败: {str(e)}")


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


# 启动服务
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5000)