-- 添加基本面数据和新闻数据缓存表
-- 版本: 1.0
-- 创建时间: 2025-09-11
-- 说明: 为股票基本面数据和新闻数据添加数据库缓存支持

USE trading_analysis;

-- ============================================================================
-- 股票基本面数据缓存表
-- ============================================================================

CREATE TABLE IF NOT EXISTS stock_fundamental_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    symbol VARCHAR(20) NOT NULL COMMENT '股票代码',
    indicator VARCHAR(50) NOT NULL COMMENT '指标类型 (all, financial_analysis, balance_sheet, profit_sheet, cash_flow_sheet)',
    period_type VARCHAR(20) NOT NULL COMMENT '报告期类型 (按报告期, 按年度, 按单季度)',
    time_period VARCHAR(10) COMMENT '时间周期 (1d, 1w, 1m, 3m, 6m, 1y, 2y, 5y)',
    data JSON NOT NULL COMMENT 'JSON格式的基本面数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_symbol_indicator (symbol, indicator),
    INDEX idx_symbol_period (symbol, period_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股票基本面数据缓存表';

-- ============================================================================
-- 财经新闻数据缓存表
-- ============================================================================

CREATE TABLE IF NOT EXISTS financial_news (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    news_id VARCHAR(36) UNIQUE NOT NULL COMMENT '新闻唯一标识',
    source VARCHAR(100) NOT NULL COMMENT '新闻来源',
    title VARCHAR(500) NOT NULL COMMENT '新闻标题',
    content TEXT COMMENT '新闻内容',
    publish_time TIMESTAMP NOT NULL COMMENT '发布时间',
    url VARCHAR(500) COMMENT '新闻链接',
    related_tickers JSON COMMENT '相关股票代码列表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_news_id (news_id),
    INDEX idx_publish_time (publish_time),
    INDEX idx_source (source),
    FULLTEXT idx_title_content (title, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财经新闻数据缓存表';

-- ============================================================================
-- 为现有表添加索引（如果需要）
-- ============================================================================

-- 确保在financial_news表中能通过related_tickers字段快速查询特定股票的新闻
-- 注意：MySQL 8.0支持JSON类型的索引，但FULLTEXT索引不支持JSON字段
-- 我们可以通过生成列来解决这个问题

-- 添加生成列以支持特定股票的索引查询
ALTER TABLE financial_news 
ADD COLUMN first_related_ticker VARCHAR(20) 
GENERATED ALWAYS AS (
    JSON_UNQUOTE(JSON_EXTRACT(related_tickers, '$[0]'))
) STORED;

-- 为生成列添加索引
CREATE INDEX idx_first_related_ticker ON financial_news (first_related_ticker);

-- 添加存储过程用于清理过期缓存数据
-- ============================================================================

DROP PROCEDURE IF EXISTS CleanupExpiredCache;
DELIMITER //
CREATE PROCEDURE CleanupExpiredCache(
    IN p_days_old INT
)
BEGIN
    -- 删除超过指定天数的基本面数据缓存
    DELETE FROM stock_fundamental_data 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL p_days_old DAY);
    
    -- 删除超过指定天数的新闻数据缓存
    DELETE FROM financial_news 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL p_days_old DAY);
    
    SELECT 
        ROW_COUNT() as deleted_records,
        'Cache cleanup completed' as status;
END //
DELIMITER ;

-- 添加存储过程用于查询特定股票的基本面数据
-- ============================================================================

DROP PROCEDURE IF EXISTS GetStockFundamentalData;
DELIMITER //
CREATE PROCEDURE GetStockFundamentalData(
    IN p_symbol VARCHAR(20),
    IN p_indicator VARCHAR(50),
    IN p_period_type VARCHAR(20),
    IN p_time_period VARCHAR(10)
)
BEGIN
    SELECT 
        data,
        created_at
    FROM stock_fundamental_data 
    WHERE symbol = p_symbol 
        AND indicator = p_indicator 
        AND period_type = p_period_type 
        AND (time_period = p_time_period OR (time_period IS NULL AND p_time_period IS NULL))
        AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)  -- 缓存有效期1天
    ORDER BY created_at DESC 
    LIMIT 1;
END //
DELIMITER ;

-- 添加存储过程用于查询特定股票的新闻数据
-- ============================================================================

DROP PROCEDURE IF EXISTS GetStockNewsData;
DELIMITER //
CREATE PROCEDURE GetStockNewsData(
    IN p_symbol VARCHAR(20),
    IN p_limit INT
)
BEGIN
    SELECT 
        news_id,
        source,
        title,
        content,
        publish_time,
        url,
        related_tickers,
        created_at
    FROM financial_news 
    WHERE JSON_CONTAINS(related_tickers, JSON_ARRAY(p_symbol)) 
       OR first_related_ticker = p_symbol
    ORDER BY publish_time DESC 
    LIMIT p_limit;
END //
DELIMITER ;

SELECT '基本面数据和新闻数据缓存表创建成功!' as status;