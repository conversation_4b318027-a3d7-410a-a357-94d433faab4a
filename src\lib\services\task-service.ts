import { Workflow } from '@/types/database';
import { api } from '../api';
import { ApiResponse } from '../api';

// 任务相关API方法 (兼容工作流)
export const taskApi = {
  // 获取任务列表 (实际查询工作流)
  getTasks: (): Promise<ApiResponse<Workflow[]>> => {
    return api.get('/api/database/tasks');
  },

  // 更新任务状态 (实际更新工作流)
  updateTaskStatus: (taskId: string, status: string): Promise<ApiResponse<void>> => {
    return api.patch(`/api/database/tasks/${taskId}/status`, { status });
  },

  // 启动任务分析
  startAnalysis: (taskData: any): Promise<ApiResponse<any>> => {
    return api.post('/api/langgraph/analyze', taskData);
  },
};