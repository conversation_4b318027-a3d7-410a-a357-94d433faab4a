'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { DataService, StockData } from '@/lib/data-service';
import {
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface StockDataSectionProps {
  ticker: string;
  workflowId?: string;
  className?: string;
}

interface StockStats {
  currentPrice: number;
  priceChange: number;
  priceChangePercent: number;
  volume: number;
  high52Week: number;
  low52Week: number;
  avgVolume: number;
  marketCap?: number;
}

export function StockDataSection({ ticker, workflowId, className = '' }: StockDataSectionProps) {
  const [stockData, setStockData] = useState<StockData[]>([]);
  const [stockStats, setStockStats] = useState<StockStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const dataService = new DataService();

  useEffect(() => {
    loadStockData();
  }, [ticker]);

  const loadStockData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await dataService.getStockData({
        ticker,
        period: '3m', // 获取3个月的数据
      });

      const data = response.data || [];
      setStockData(data);

      if (data.length > 0) {
        calculateStockStats(data);
      }
    } catch (err) {
      console.error('加载股票数据失败:', err);
      setError(err instanceof Error ? err.message : '加载股票数据失败');
    } finally {
      setLoading(false);
    }
  };

  const calculateStockStats = (data: StockData[]) => {
    if (data.length === 0) return;

    const latest = data[0]; // 最新数据
    const previous = data[1]; // 前一天数据

    const priceChange = previous ? latest.close - previous.close : 0;
    const priceChangePercent = previous ? (priceChange / previous.close) * 100 : 0;

    const high52Week = Math.max(...data.map(d => d.high));
    const low52Week = Math.min(...data.map(d => d.low));
    const avgVolume = data.reduce((sum, d) => sum + d.volume, 0) / data.length;

    setStockStats({
      currentPrice: latest.close,
      priceChange,
      priceChangePercent,
      volume: latest.volume,
      high52Week,
      low52Week,
      avgVolume,
    });
  };

  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  const formatVolume = (volume: number) => {
    if (volume >= 100000000) {
      return `${(volume / 100000000).toFixed(1)}亿`;
    } else if (volume >= 10000) {
      return `${(volume / 10000).toFixed(1)}万`;
    }
    return volume.toString();
  };

  const formatPercent = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ChartBarIcon className="h-5 w-5" />
            <span>股票数据</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
            <span className="ml-3 text-slate-600 dark:text-slate-400">加载股票数据...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ChartBarIcon className="h-5 w-5" />
            <span>股票数据</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 dark:text-red-400">{error}</p>
            <button
              onClick={loadStockData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              重试
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ChartBarIcon className="h-5 w-5" />
            <span>股票数据</span>
          </div>
          <span className="text-sm font-normal text-slate-500">
            {ticker}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 股价概览 */}
        {stockStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* 当前价格 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-blue-600 dark:text-blue-400">当前价格</p>
                  <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                    ¥{formatPrice(stockStats.currentPrice)}
                  </p>
                </div>
                <CurrencyDollarIcon className="h-8 w-8 text-blue-500" />
              </div>
            </motion.div>

            {/* 涨跌幅 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
              className={`p-4 rounded-lg ${
                stockStats.priceChangePercent >= 0
                  ? 'bg-green-50 dark:bg-green-900/20'
                  : 'bg-red-50 dark:bg-red-900/20'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm ${
                    stockStats.priceChangePercent >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    涨跌幅
                  </p>
                  <p className={`text-2xl font-bold ${
                    stockStats.priceChangePercent >= 0
                      ? 'text-green-700 dark:text-green-300'
                      : 'text-red-700 dark:text-red-300'
                  }`}>
                    {formatPercent(stockStats.priceChangePercent)}
                  </p>
                  <p className={`text-sm ${
                    stockStats.priceChangePercent >= 0
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {stockStats.priceChange >= 0 ? '+' : ''}{formatPrice(stockStats.priceChange)}
                  </p>
                </div>
                {stockStats.priceChangePercent >= 0 ? (
                  <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />
                ) : (
                  <ArrowTrendingDownIcon className="h-8 w-8 text-red-500" />
                )}
              </div>
            </motion.div>

            {/* 成交量 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-purple-600 dark:text-purple-400">成交量</p>
                  <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                    {formatVolume(stockStats.volume)}
                  </p>
                </div>
                <ChartBarIcon className="h-8 w-8 text-purple-500" />
              </div>
            </motion.div>

            {/* 52周区间 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
            >
              <div>
                <p className="text-sm text-orange-600 dark:text-orange-400 mb-2">52周区间</p>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-orange-700 dark:text-orange-300">最高</span>
                    <span className="font-semibold">¥{formatPrice(stockStats.high52Week)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-orange-700 dark:text-orange-300">最低</span>
                    <span className="font-semibold">¥{formatPrice(stockStats.low52Week)}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        )}

        {/* 最近交易数据 */}
        <div>
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">最近交易数据</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-slate-200 dark:border-slate-700">
                  <th className="text-left py-2 text-slate-600 dark:text-slate-400">日期</th>
                  <th className="text-right py-2 text-slate-600 dark:text-slate-400">开盘</th>
                  <th className="text-right py-2 text-slate-600 dark:text-slate-400">最高</th>
                  <th className="text-right py-2 text-slate-600 dark:text-slate-400">最低</th>
                  <th className="text-right py-2 text-slate-600 dark:text-slate-400">收盘</th>
                  <th className="text-right py-2 text-slate-600 dark:text-slate-400">成交量</th>
                  <th className="text-right py-2 text-slate-600 dark:text-slate-400">涨跌幅</th>
                </tr>
              </thead>
              <tbody>
                {stockData.slice(0, 10).map((data, index) => (
                  <motion.tr
                    key={data.date}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="border-b border-slate-100 dark:border-slate-800 hover:bg-slate-50 dark:hover:bg-slate-800/50"
                  >
                    <td className="py-2 text-slate-900 dark:text-white">
                      {new Date(data.date).toLocaleDateString('zh-CN')}
                    </td>
                    <td className="text-right py-2 text-slate-700 dark:text-slate-300">
                      {formatPrice(data.open)}
                    </td>
                    <td className="text-right py-2 text-slate-700 dark:text-slate-300">
                      {formatPrice(data.high)}
                    </td>
                    <td className="text-right py-2 text-slate-700 dark:text-slate-300">
                      {formatPrice(data.low)}
                    </td>
                    <td className="text-right py-2 text-slate-700 dark:text-slate-300">
                      {formatPrice(data.close)}
                    </td>
                    <td className="text-right py-2 text-slate-700 dark:text-slate-300">
                      {formatVolume(data.volume)}
                    </td>
                    <td className={`text-right py-2 font-medium ${
                      data.pctChange >= 0
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {formatPercent(data.pctChange)}
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {stockData.length === 0 && (
          <div className="text-center py-8">
            <ChartBarIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-500">暂无股票数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
