{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "autoApprove": []}, "mysql": {"command": "node", "args": ["../product/mysql-mcp/dist/index.js"], "env": {"MYSQL_HOST": "************", "MYSQL_PORT": "13306", "MYSQL_USER": "root", "MYSQL_PASS": "trading123", "MYSQL_DB": "trading_analysis"}}, "mcp-deepwiki": {"type": "stdio", "command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}}}