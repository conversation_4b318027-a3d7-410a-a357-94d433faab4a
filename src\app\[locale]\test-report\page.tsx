'use client';

import { EnhancedAnalysisReportViewer } from '@/components/dashboard/EnhancedAnalysisReportViewer';
import { FundamentalDataSection } from '@/components/dashboard/FundamentalDataSection';
import { NewsDataSection } from '@/components/dashboard/NewsDataSection';
import { StockDataSection } from '@/components/dashboard/StockDataSection';
import { useState } from 'react';

// 模拟数据
const mockAnalystReports = [
  {
    id: 1,
    report_id: 'analyst-1',
    analyst_type: 'fundamental' as const,
    summary:
      '# 基本面分析报告\n\n## 财务状况\n公司财务状况良好，营收稳定增长，净利润率保持在合理水平。\n\n## 估值分析\n当前PE比率为15.2，相对于行业平均水平略低，具有一定的投资价值。',
    status: 'completed' as const,
    execution_time_ms: 5000,
    created_at: new Date().toISOString(),
  },
  {
    id: 2,
    report_id: 'analyst-2',
    analyst_type: 'technical' as const,
    summary:
      '# 技术分析报告\n\n## 趋势分析\n股价处于上升通道中，短期均线向上发散，技术面偏强。\n\n## 支撑阻力\n支撑位：¥45.20，阻力位：¥52.80',
    status: 'completed' as const,
    execution_time_ms: 3000,
    created_at: new Date().toISOString(),
  },
  {
    id: 3,
    report_id: 'analyst-3',
    analyst_type: 'sentiment' as const,
    summary:
      '# 情绪分析报告\n\n## 市场情绪\n整体市场情绪偏乐观，投资者信心指数较高。\n\n## 社交媒体分析\n相关讨论量增加，正面评价占比65%。',
    status: 'completed' as const,
    execution_time_ms: 4000,
    created_at: new Date().toISOString(),
  },
  {
    id: 4,
    report_id: 'analyst-4',
    analyst_type: 'news' as const,
    summary:
      '# 新闻分析报告\n\n## 重要新闻\n公司发布Q3财报，业绩超预期。新产品发布会获得市场积极反响。\n\n## 影响评估\n正面新闻占主导，预计对股价产生积极影响。',
    status: 'completed' as const,
    execution_time_ms: 2500,
    created_at: new Date().toISOString(),
  },
];

const mockResearchReports = [
  {
    id: 1,
    report_id: 'research-1',
    researcher_type: 'bull' as const,
    summary:
      '# 多头研究报告\n\n## 投资亮点\n1. 行业领先地位稳固\n2. 技术创新能力强\n3. 财务状况健康\n\n## 目标价位\n建议目标价¥55.00，上涨空间约15%。',
    confidence_level: 0.85,
    target_price: 55.0,
    time_horizon: '6个月',
    status: 'completed' as const,
    created_at: new Date().toISOString(),
  },
  {
    id: 2,
    report_id: 'research-2',
    researcher_type: 'bear' as const,
    summary:
      '# 空头研究报告\n\n## 风险因素\n1. 行业竞争加剧\n2. 原材料成本上升\n3. 监管政策不确定性\n\n## 谨慎建议\n建议谨慎观望，关注风险因素变化。',
    confidence_level: 0.65,
    target_price: 42.0,
    time_horizon: '3个月',
    status: 'completed' as const,
    created_at: new Date().toISOString(),
  },
];

const mockFinalDecision = {
  id: 1,
  decision_id: 'decision-1',
  workflow_id: 'test-workflow',
  decision_type: 'buy' as const,
  confidence_level: 0.75,
  decision_rationale:
    '# 最终投资决策\n\n## 综合评估\n基于多维度分析，该股票具有较好的投资价值。基本面稳健，技术面偏强，市场情绪积极。\n\n## 投资建议\n建议**买入**，目标价位¥50.00，预期6个月内达到。\n\n## 风险提示\n需关注行业政策变化和市场整体波动风险。',
  created_at: new Date(),
};

export default function TestReportPage() {
  const [activeView, setActiveView] = useState<'enhanced' | 'individual'>('enhanced');

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
            报告展示测试页面
          </h1>
          <p className="text-slate-600 dark:text-slate-400">测试新的报告展示组件和数据可视化功能</p>
        </div>

        {/* 视图切换 */}
        <div className="flex justify-center mb-8">
          <div className="bg-slate-100 dark:bg-slate-800 p-1 rounded-lg">
            <button
              onClick={() => setActiveView('enhanced')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-colors ${
                activeView === 'enhanced'
                  ? 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm'
                  : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
              }`}
            >
              增强版报告查看器
            </button>
            <button
              onClick={() => setActiveView('individual')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-colors ${
                activeView === 'individual'
                  ? 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm'
                  : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
              }`}
            >
              独立数据组件
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        {activeView === 'enhanced' ? (
          <EnhancedAnalysisReportViewer
            ticker="600519"
            workflowId="test-workflow-123"
            analystReports={mockAnalystReports}
            researchReports={mockResearchReports}
            finalDecision={mockFinalDecision}
          />
        ) : (
          <div className="space-y-8">
            <StockDataSection ticker="600519" workflowId="test-workflow-123" />
            <FundamentalDataSection ticker="600519" workflowId="test-workflow-123" />
            <NewsDataSection ticker="600519" workflowId="test-workflow-123" />
          </div>
        )}
      </div>
    </div>
  );
}
