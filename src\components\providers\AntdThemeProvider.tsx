'use client';

import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import { useLocale } from 'next-intl';
import { useTheme } from './ThemeProvider';
import { getAntdTheme } from '@/lib/antd-theme';

interface AntdThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Ant Design 主题提供者
 * 为整个应用提供统一的 antd 主题配置
 */
export function AntdThemeProvider({ children }: AntdThemeProviderProps) {
  const { resolvedTheme } = useTheme();
  const locale = useLocale();
  
  // 获取 antd 主题配置
  const theme = getAntdTheme(resolvedTheme === 'dark');
  
  // 获取 antd 语言包
  const antdLocale = locale === 'zh' ? zhCN : enUS;
  
  return (
    <ConfigProvider 
      theme={theme} 
      locale={antdLocale}
      componentSize="middle"
    >
      {children}
    </ConfigProvider>
  );
}
