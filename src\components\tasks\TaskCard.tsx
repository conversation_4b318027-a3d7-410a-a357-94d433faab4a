'use client';

import { AnalysisStatus } from '@/hooks/useAnalysisStatus';
import { Workflow } from '@/types/database';
import { getAnalysisPeriodLabel, getResearchDepthLabel } from '@/utils/enums';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { AnalysisActionButton } from './AnalysisActionButton';
import { get } from 'lodash';
import { getAnalystLabel } from '@/utils/options';

interface TaskCardProps {
  task: Workflow;
  analysisStatus?: AnalysisStatus;
  onViewAnalysis?: (task: Workflow) => Promise<void>;
  onStartAnalysis?: (task: Workflow) => Promise<void>;
  onViewMessages?: (taskId: string) => Promise<void>;
  startingTask?: string | null;
  className?: string;
}

export function TaskCard({
  task,
  analysisStatus,
  onViewAnalysis,
  onStartAnalysis,
  onViewMessages,
  startingTask,
  className = '',
}: TaskCardProps) {
  // 获取任务状态样式
  const getTaskStatusStyle = (status: Workflow['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'running':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'cancelled':
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
      default:
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
    }
  };

  // 获取任务状态文本
  const getTaskStatusText = (status: Workflow['status']) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '运行中';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '待处理';
    }
  };

  // 获取分析状态文本
  const getAnalysisStatusText = (status?: string) => {
    switch (status) {
      case 'completed':
        return '分析完成';
      case 'running':
        return '分析中';
      case 'failed':
        return '分析失败';
      default:
        return '未开始';
    }
  };

  // 获取分析状态样式
  const getAnalysisStatusStyle = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'running':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
    }
  };

  return (
    <article
      className={`bg-white flex flex-col dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 ${className}`}
      role="article"
      aria-labelledby={`task-title-${task.workflow_id}`}
      aria-describedby={`task-description-${task.workflow_id}`}
    >
      {/* 卡片头部 */}
      <header className="p-4 border-b border-gray-100 dark:border-slate-600">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <h3
                id={`task-title-${task.workflow_id}`}
                className="text-lg font-semibold text-gray-900 dark:text-white truncate"
              >
                {task.ticker}
              </h3>
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTaskStatusStyle(
                  task.status
                )}`}
              >
                {getTaskStatusText(task.status)}
              </span>
            </div>
            <p
              id={`task-description-${task.workflow_id}`}
              className="text-sm text-gray-600 dark:text-slate-300 line-clamp-2"
              title={task.title}
            >
              {task.title}
            </p>
            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-slate-400">
              <span>
                创建于{' '}
                {task.created_at
                  ? format(new Date(task.created_at), 'MM-dd HH:mm', { locale: zhCN })
                  : '-'}
              </span>
              {task.created_by && <span>创建者: {task.created_by}</span>}
            </div>
          </div>
        </div>
      </header>

      {/* 任务详情 */}
      <section className="p-4 space-y-3 flex-1" aria-label="任务详细信息">
        {/* 配置信息 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500 dark:text-slate-400">研究深度:</span>
            <span className="ml-2 text-gray-900 dark:text-white">
              {task.research_depth ? getResearchDepthLabel(task.research_depth) : '-'}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-slate-400">分析周期:</span>
            <span className="ml-2 text-gray-900 dark:text-white">
              {task.analysis_period ? getAnalysisPeriodLabel(task.analysis_period) : '-'}
            </span>
          </div>
        </div>

        {/* 分析状态 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span
              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getAnalysisStatusStyle(
                analysisStatus?.status
              )}`}
            >
              {getAnalysisStatusText(analysisStatus?.status)}
            </span>
            {analysisStatus?.status === 'running' && (
              <span className="text-xs text-gray-500 dark:text-slate-400">
                {analysisStatus.progress}%
              </span>
            )}
          </div>

          {/* 进度条 */}
          {analysisStatus?.status === 'running' && (
            <div className="w-full bg-gray-200 dark:bg-slate-600 rounded-full h-1.5">
              <div
                className="bg-blue-600 dark:bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${analysisStatus.progress}%` }}
              />
            </div>
          )}

          {/* 当前阶段 */}
          {analysisStatus?.status === 'running' && (
            <div className="text-xs text-gray-600 dark:text-slate-300">
              当前阶段: {analysisStatus.currentStage}
            </div>
          )}
        </div>

        {/* 分析摘要 */}
        {task.analysis_summary && Object.keys(task.analysis_summary).length > 0 && (
          <div className="pt-2 border-t border-slate-100 dark:border-slate-700">
            <h4 className="text-sm font-medium text-slate-600 dark:text-slate-300 mb-2">
              分析摘要
            </h4>
            <div className="flex flex-wrap gap-2">
              {Object.entries(task.analysis_summary).map(([analyst, summary]) => (
                <div
                  key={analyst}
                  className="bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded-md text-xs"
                >
                  <span className="font-semibold text-slate-700 dark:text-slate-200">
                    {getAnalystLabel(analyst)}:
                  </span>
                  <span className="ml-1 text-slate-600 dark:text-slate-300">{summary}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {task.error_message && (
          <div className="p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-xs text-red-700 dark:text-red-300">
            {task.error_message}
          </div>
        )}
      </section>

      {/* 操作按钮 */}
      <footer className="px-4 py-3 bg-slate-50 dark:bg-slate-700 border-t border-slate-100 dark:border-slate-600 rounded-b-lg">
        <div className="flex items-center justify-between">
          <nav className="flex space-x-3" aria-label="任务操作">
            <button
              onClick={() => onViewMessages?.(task.workflow_id)}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors"
              aria-label={`查看任务 ${task.ticker} 的消息`}
            >
              消息
            </button>
          </nav>

          <AnalysisActionButton
            task={task}
            analysisStatus={analysisStatus}
            onStartAnalysis={onStartAnalysis}
            onViewAnalysis={onViewAnalysis}
            startingTask={startingTask}
          />
        </div>
      </footer>
    </article>
  );
}
