import { api } from '../api';
import { ApiResponse } from '../api';

// 风险数据相关 API 方法
export const riskDataApi = {
  // 保存风险评估
  saveRiskAssessment: (data: any): Promise<ApiResponse<{ risk_id: string }>> => {
    return api.post('/api/risk-data', data);
  },

  // 获取风险评估
  getRiskAssessment: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get('/api/risk-data', { params: { workflow_id: workflowId } });
  },

  // 获取风险评估历史
  getRiskAssessmentHistory: (params: {
    workflow_id?: string;
    ticker?: string;
    date_from?: string;
    date_to?: string;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<any>> => {
    return api.get('/api/risk-data', { params });
  },

  // 获取风险指标
  getRiskMetrics: (params: {
    workflow_id?: string;
    ticker?: string;
    date_from?: string;
    date_to?: string;
  }): Promise<ApiResponse<any>> => {
    return api.get('/api/risk-data/metrics', { params });
  },

  // 比较风险评估
  compareRiskAssessments: (workflowIds: string[]): Promise<ApiResponse<any>> => {
    return api.post('/api/risk-data/compare', { workflow_ids: workflowIds });
  },
};