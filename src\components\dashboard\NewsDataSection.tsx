'use client';

import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { DataService, NewsData } from '@/lib/data-service';
import {
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  CalendarIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  HeartIcon,
  NewspaperIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface NewsDataSectionProps {
  ticker: string;
  workflowId?: string;
  className?: string;
}

interface NewsStats {
  totalCount: number;
  sentimentDistribution: {
    positive: number;
    negative: number;
    neutral: number;
  };
  avgSentiment: number;
  sentimentLabel: string;
  categoryStats: { [key: string]: number };
  sourceStats: { [key: string]: number };
  timeRange: {
    latest?: string;
    earliest?: string;
  };
}

export function NewsDataSection({ ticker, workflowId, className = '' }: NewsDataSectionProps) {
  const [newsData, setNewsData] = useState<NewsData[]>([]);
  const [newsStats, setNewsStats] = useState<NewsStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedNews, setExpandedNews] = useState<Set<string>>(new Set());

  const dataService = new DataService();

  useEffect(() => {
    loadNewsData();
  }, [ticker]);

  const loadNewsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await dataService.getNewsData({
        ticker,
        limit: 20,
        days: 7,
      });

      setNewsData(response.news || []);
      setNewsStats(response.stats);
    } catch (err) {
      console.error('加载新闻数据失败:', err);
      setError(err instanceof Error ? err.message : '加载新闻数据失败');
    } finally {
      setLoading(false);
    }
  };

  const toggleNewsExpansion = (newsId: string) => {
    const newExpanded = new Set(expandedNews);
    if (newExpanded.has(newsId)) {
      newExpanded.delete(newsId);
    } else {
      newExpanded.add(newsId);
    }
    setExpandedNews(newExpanded);
  };

  const getSentimentIcon = (sentiment: number) => {
    if (sentiment > 0.1) {
      return <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />;
    } else if (sentiment < -0.1) {
      return <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />;
    } else {
      return <HeartIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.1) return 'text-green-600 bg-green-50 border-green-200';
    if (sentiment < -0.1) return 'text-red-600 bg-red-50 border-red-200';
    return 'text-gray-600 bg-gray-50 border-gray-200';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <NewspaperIcon className="h-5 w-5" />
            <span>相关新闻</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
            <span className="ml-3 text-slate-600 dark:text-slate-400">加载新闻数据...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <NewspaperIcon className="h-5 w-5" />
            <span>相关新闻</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 dark:text-red-400">{error}</p>
            <button
              onClick={loadNewsData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              重试
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <NewspaperIcon className="h-5 w-5" />
            <span>相关新闻</span>
          </div>
          <span className="text-sm font-normal text-slate-500">共 {newsData.length} 条新闻</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 新闻统计 */}
        {newsStats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600 dark:text-slate-400">整体情绪</p>
                  <p className="text-lg font-semibold text-slate-900 dark:text-white">
                    {newsStats.sentimentLabel}
                  </p>
                </div>
                <ChartBarIcon className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-green-600 dark:text-green-400">正面新闻</p>
                  <p className="text-lg font-semibold text-green-700 dark:text-green-300">
                    {newsStats.sentimentDistribution.positive} 条
                  </p>
                </div>
                <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />
              </div>
            </div>
            <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-red-600 dark:text-red-400">负面新闻</p>
                  <p className="text-lg font-semibold text-red-700 dark:text-red-300">
                    {newsStats.sentimentDistribution.negative} 条
                  </p>
                </div>
                <ArrowTrendingDownIcon className="h-8 w-8 text-red-500" />
              </div>
            </div>
          </div>
        )}

        {/* 新闻列表 */}
        <div className="space-y-4">
          {newsData.map((news, index) => {
            const isExpanded = expandedNews.has(news.id);
            return (
              <motion.div
                key={news.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-medium text-slate-900 dark:text-white mb-2 line-clamp-2">
                        {news.title}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-slate-500">
                        <span className="flex items-center space-x-1">
                          <CalendarIcon className="h-4 w-4" />
                          <span>{formatDate(news.publishedAt)}</span>
                        </span>
                        <span>{news.source}</span>
                        <Badge className={`text-xs ${getSentimentColor(news.sentiment)}`}>
                          {getSentimentIcon(news.sentiment)}
                          <span className="ml-1">
                            {news.sentiment > 0.1
                              ? '正面'
                              : news.sentiment < -0.1
                              ? '负面'
                              : '中性'}
                          </span>
                        </Badge>
                      </div>
                    </div>
                    <button
                      onClick={() => toggleNewsExpansion(news.id)}
                      className="ml-4 p-2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                  </div>

                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="border-t border-slate-200 dark:border-slate-700 pt-3"
                      >
                        <p className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed mb-3">
                          {news.summary}
                        </p>
                        {news.url && (
                          <a
                            href={news.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                          >
                            查看原文 →
                          </a>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>
            );
          })}
        </div>

        {newsData.length === 0 && (
          <div className="text-center py-8">
            <NewspaperIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-500">暂无相关新闻</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
