'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { DataService, FundamentalData } from '@/lib/data-service';
import {
  BanknotesIcon,
  BuildingOfficeIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  ScaleIcon,
  TrendingUpIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface FundamentalDataSectionProps {
  ticker: string;
  workflowId?: string;
  className?: string;
}

export function FundamentalDataSection({ ticker, workflowId, className = '' }: FundamentalDataSectionProps) {
  const [fundamentalData, setFundamentalData] = useState<FundamentalData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const dataService = new DataService();

  useEffect(() => {
    loadFundamentalData();
  }, [ticker]);

  const loadFundamentalData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await dataService.getFundamentalData({
        ticker,
        type: 'all',
        period: 'annual',
      });

      setFundamentalData(response.fundamentals || null);
    } catch (err) {
      console.error('加载基本面数据失败:', err);
      setError(err instanceof Error ? err.message : '加载基本面数据失败');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (value: number | null, unit: string = '') => {
    if (value === null || value === undefined) return 'N/A';
    
    if (Math.abs(value) >= 100000000) {
      return `${(value / 100000000).toFixed(2)}亿${unit}`;
    } else if (Math.abs(value) >= 10000) {
      return `${(value / 10000).toFixed(2)}万${unit}`;
    }
    return `${value.toFixed(2)}${unit}`;
  };

  const formatPercent = (value: number | null) => {
    if (value === null || value === undefined) return 'N/A';
    return `${value.toFixed(2)}%`;
  };

  const formatRatio = (value: number | null) => {
    if (value === null || value === undefined) return 'N/A';
    return value.toFixed(2);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BuildingOfficeIcon className="h-5 w-5" />
            <span>基本面数据</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
            <span className="ml-3 text-slate-600 dark:text-slate-400">加载基本面数据...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BuildingOfficeIcon className="h-5 w-5" />
            <span>基本面数据</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 dark:text-red-400">{error}</p>
            <button
              onClick={loadFundamentalData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              重试
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!fundamentalData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BuildingOfficeIcon className="h-5 w-5" />
            <span>基本面数据</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <BuildingOfficeIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-500">暂无基本面数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BuildingOfficeIcon className="h-5 w-5" />
            <span>基本面数据</span>
          </div>
          <span className="text-sm font-normal text-slate-500">
            {ticker}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 财务概览 */}
        <div>
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
            <CurrencyDollarIcon className="h-5 w-5 mr-2" />
            财务概览
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
            >
              <p className="text-sm text-blue-600 dark:text-blue-400">营业收入</p>
              <p className="text-xl font-bold text-blue-700 dark:text-blue-300">
                {formatNumber(fundamentalData.financials.revenue, '元')}
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
              className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg"
            >
              <p className="text-sm text-green-600 dark:text-green-400">净利润</p>
              <p className="text-xl font-bold text-green-700 dark:text-green-300">
                {formatNumber(fundamentalData.financials.netIncome, '元')}
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg"
            >
              <p className="text-sm text-purple-600 dark:text-purple-400">每股收益</p>
              <p className="text-xl font-bold text-purple-700 dark:text-purple-300">
                {formatNumber(fundamentalData.financials.eps, '元')}
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
            >
              <p className="text-sm text-orange-600 dark:text-orange-400">毛利润</p>
              <p className="text-xl font-bold text-orange-700 dark:text-orange-300">
                {formatNumber(fundamentalData.financials.grossProfit, '元')}
              </p>
            </motion.div>
          </div>
        </div>

        {/* 估值指标 */}
        <div>
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
            <ScaleIcon className="h-5 w-5 mr-2" />
            估值指标
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">市盈率 (PE)</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatRatio(fundamentalData.valuation.pe)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">市净率 (PB)</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatRatio(fundamentalData.valuation.pb)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">市销率 (PS)</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatRatio(fundamentalData.valuation.ps)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">市现率 (PCF)</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatRatio(fundamentalData.valuation.pcf)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">企业价值 (EV)</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatNumber(fundamentalData.valuation.ev, '元')}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">市值</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatNumber(fundamentalData.valuation.marketCap, '元')}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 增长指标 */}
        <div>
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
            <TrendingUpIcon className="h-5 w-5 mr-2" />
            增长指标
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">营收增长率</span>
                <span className={`font-semibold ${
                  (fundamentalData.growth.revenueGrowth || 0) >= 0
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {formatPercent(fundamentalData.growth.revenueGrowth)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">净利润增长率</span>
                <span className={`font-semibold ${
                  (fundamentalData.growth.netIncomeGrowth || 0) >= 0
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {formatPercent(fundamentalData.growth.netIncomeGrowth)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">EPS增长率</span>
                <span className={`font-semibold ${
                  (fundamentalData.growth.epsGrowth || 0) >= 0
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {formatPercent(fundamentalData.growth.epsGrowth)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">营业利润增长率</span>
                <span className={`font-semibold ${
                  (fundamentalData.growth.operatingIncomeGrowth || 0) >= 0
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {formatPercent(fundamentalData.growth.operatingIncomeGrowth)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 财务比率 */}
        <div>
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
            <ChartBarIcon className="h-5 w-5 mr-2" />
            财务比率
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">净资产收益率 (ROE)</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatPercent(fundamentalData.ratios.roe)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">总资产收益率 (ROA)</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatPercent(fundamentalData.ratios.roa)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">毛利率</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatPercent(fundamentalData.ratios.grossMargin)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">净利率</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatPercent(fundamentalData.ratios.netMargin)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">流动比率</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatRatio(fundamentalData.ratios.currentRatio)}
                </span>
              </div>
            </div>
            <div className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600 dark:text-slate-400">资产负债率</span>
                <span className="font-semibold text-slate-900 dark:text-white">
                  {formatRatio(fundamentalData.ratios.debtToEquity)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
