'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/Tabs';
import { FinalDecision } from '@/types/langgraph-database';
import {
  ArrowDownTrayIcon,
  ChartBarIcon,
  ClockIcon,
  DocumentTextIcon,
  EyeIcon,
  PrinterIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { FundamentalDataSection } from './FundamentalDataSection';
import { NewsDataSection } from './NewsDataSection';
import { StockDataSection } from './StockDataSection';

// 类型定义
interface AnalystReport {
  id: number;
  report_id: string;
  analyst_type: 'fundamental' | 'technical' | 'sentiment' | 'news';
  summary?: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
  created_at: string;
}

interface ResearchReport {
  id: number;
  report_id: string;
  researcher_type: 'bull' | 'bear';
  summary?: string;
  confidence_level?: number;
  target_price?: number;
  time_horizon?: string;
  status: 'completed' | 'failed';
  created_at: string;
}

interface EnhancedAnalysisReportViewerProps {
  ticker: string;
  workflowId?: string;
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
  finalDecision?: FinalDecision | null;
  className?: string;
}

export function EnhancedAnalysisReportViewer({
  ticker,
  workflowId,
  analystReports,
  researchReports,
  finalDecision,
  className = '',
}: EnhancedAnalysisReportViewerProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'data' | 'analysts' | 'research' | 'decision'>('overview');
  const [expandedReports, setExpandedReports] = useState<Set<string>>(new Set());
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const toggleReportExpansion = (reportId: string) => {
    const newExpanded = new Set(expandedReports);
    if (newExpanded.has(reportId)) {
      newExpanded.delete(reportId);
    } else {
      newExpanded.add(reportId);
    }
    setExpandedReports(newExpanded);
  };

  const handleExportPDF = async () => {
    setIsGeneratingPDF(true);
    try {
      // TODO: 实现PDF导出功能
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟导出过程
      console.log('PDF导出功能待实现');
    } catch (error) {
      console.error('PDF导出失败:', error);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const completedAnalystReports = analystReports.filter(
    (report) => report.status === 'completed' && report.summary
  );

  const completedResearchReports = researchReports.filter(
    (report) => report.status === 'completed' && report.summary
  );

  const getAnalystName = (type: string) => {
    const names = {
      fundamental: '基本面分析师',
      technical: '技术分析师',
      sentiment: '情绪分析师',
      news: '新闻分析师',
    };
    return names[type as keyof typeof names] || type;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <DocumentTextIcon className="h-6 w-6" />
            <span>增强版分析报告</span>
            <Badge variant="outline" className="ml-2">
              {ticker}
            </Badge>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrint}
              className="flex items-center space-x-1"
            >
              <PrinterIcon className="h-4 w-4" />
              <span>打印</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportPDF}
              disabled={isGeneratingPDF}
              className="flex items-center space-x-1"
            >
              {isGeneratingPDF ? (
                <LoadingSpinner size="sm" />
              ) : (
                <ArrowDownTrayIcon className="h-4 w-4" />
              )}
              <span>{isGeneratingPDF ? '生成中...' : '导出PDF'}</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="data">数据分析</TabsTrigger>
            <TabsTrigger value="analysts">分析师报告</TabsTrigger>
            <TabsTrigger value="research">研究员报告</TabsTrigger>
            <TabsTrigger value="decision">最终决策</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* 报告统计 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">分析师报告</p>
                        <p className="text-2xl font-bold text-slate-900 dark:text-white">
                          {completedAnalystReports.length}
                        </p>
                      </div>
                      <ChartBarIcon className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">研究员报告</p>
                        <p className="text-2xl font-bold text-slate-900 dark:text-white">
                          {completedResearchReports.length}
                        </p>
                      </div>
                      <DocumentTextIcon className="h-8 w-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">最终决策</p>
                        <p className="text-2xl font-bold text-slate-900 dark:text-white">
                          {finalDecision ? '已完成' : '待生成'}
                        </p>
                      </div>
                      <ClockIcon className="h-8 w-8 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 执行摘要 */}
              <Card>
                <CardHeader>
                  <CardTitle>执行摘要</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-slate dark:prose-invert max-w-none">
                    <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                      本报告基于多智能体协作分析框架，对股票代码 <strong>{ticker}</strong> 进行了全面的投资分析。
                      分析团队包括 {completedAnalystReports.length} 名专业分析师和 {completedResearchReports.length} 名研究员，
                      从基本面、技术面、情绪面和新闻面等多个维度进行了深入研究。
                    </p>
                    {finalDecision && (
                      <p className="text-slate-700 dark:text-slate-300 leading-relaxed mt-4">
                        经过综合评估，团队给出了 <strong>{finalDecision.decision_type}</strong> 的投资建议，
                        置信度为 <strong>{Math.round((finalDecision.confidence_level || 0) * 100)}%</strong>。
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 关键发现 */}
              <Card>
                <CardHeader>
                  <CardTitle>关键发现</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {completedAnalystReports.map((report, index) => {
                      const summary = report.summary?.split('\n')[0] || ''; // 取第一行作为关键发现
                      return (
                        <motion.div
                          key={report.report_id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-start space-x-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg"
                        >
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-slate-900 dark:text-white">
                              {getAnalystName(report.analyst_type)}
                            </p>
                            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                              {summary.length > 100 ? `${summary.substring(0, 100)}...` : summary}
                            </p>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          <TabsContent value="data" className="mt-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* 股票数据 */}
              <StockDataSection ticker={ticker} workflowId={workflowId} />
              
              {/* 基本面数据 */}
              <FundamentalDataSection ticker={ticker} workflowId={workflowId} />
              
              {/* 新闻数据 */}
              <NewsDataSection ticker={ticker} workflowId={workflowId} />
            </motion.div>
          </TabsContent>

          <TabsContent value="analysts" className="mt-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              {completedAnalystReports.map((report, index) => (
                <Card key={report.report_id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <span>{getAnalystName(report.analyst_type)}</span>
                        <Badge variant="outline">
                          {new Date(report.created_at).toLocaleDateString()}
                        </Badge>
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleReportExpansion(report.report_id)}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <AnimatePresence>
                      {expandedReports.has(report.report_id) ? (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="prose prose-slate dark:prose-invert max-w-none"
                        >
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {report.summary || ''}
                          </ReactMarkdown>
                        </motion.div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="text-slate-600 dark:text-slate-400"
                        >
                          {(report.summary || '').substring(0, 200)}...
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="research" className="mt-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              {completedResearchReports.map((report, index) => (
                <Card key={report.report_id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <span>{report.researcher_type === 'bull' ? '多头研究员' : '空头研究员'}</span>
                        <Badge variant="outline">
                          {new Date(report.created_at).toLocaleDateString()}
                        </Badge>
                        {report.confidence_level && (
                          <Badge variant="secondary">
                            置信度: {Math.round(report.confidence_level * 100)}%
                          </Badge>
                        )}
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleReportExpansion(report.report_id)}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <AnimatePresence>
                      {expandedReports.has(report.report_id) ? (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="prose prose-slate dark:prose-invert max-w-none"
                        >
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {report.summary || ''}
                          </ReactMarkdown>
                        </motion.div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="text-slate-600 dark:text-slate-400"
                        >
                          {(report.summary || '').substring(0, 200)}...
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              ))}
            </motion.div>
          </TabsContent>

          <TabsContent value="decision" className="mt-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              {finalDecision ? (
                <Card>
                  <CardHeader>
                    <CardTitle>最终投资决策</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <p className="text-sm text-blue-600 dark:text-blue-400">决策类型</p>
                          <p className="text-xl font-bold text-blue-700 dark:text-blue-300">
                            {finalDecision.decision_type}
                          </p>
                        </div>
                        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <p className="text-sm text-green-600 dark:text-green-400">置信度</p>
                          <p className="text-xl font-bold text-green-700 dark:text-green-300">
                            {Math.round((finalDecision.confidence_level || 0) * 100)}%
                          </p>
                        </div>
                        <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                          <p className="text-sm text-purple-600 dark:text-purple-400">目标价格</p>
                          <p className="text-xl font-bold text-purple-700 dark:text-purple-300">
                            {finalDecision.target_price ? `¥${finalDecision.target_price}` : 'N/A'}
                          </p>
                        </div>
                      </div>
                      <div className="prose prose-slate dark:prose-invert max-w-none">
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {finalDecision.decision_rationale || ''}
                        </ReactMarkdown>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="text-center py-12">
                    <ClockIcon className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                      最终决策生成中
                    </h3>
                    <p className="text-slate-600 dark:text-slate-400">
                      请等待所有分析完成后生成最终投资决策
                    </p>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
