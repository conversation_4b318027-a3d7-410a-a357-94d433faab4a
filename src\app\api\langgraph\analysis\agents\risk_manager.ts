import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { RiskDataCollection, riskDataCollector } from '@/lib/risk-data-collector';
import { riskManagerStateManager } from '@/lib/risk-manager-state';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';

/**
 * 风险管理师智能体
 * 负责评估投资风险并提供风险控制建议
 */
export async function riskManagerNode(state: typeof TradingAgentAnnotation.State) {
  console.log('[Risk Manager] Starting comprehensive risk assessment...');

  const { config } = state;
  if (config.selected_analysts && !config.selected_analysts.includes('risk')) {
    console.log(`[Risk Manager] Skipping risk assessment as per configuration`);
    return { messages: state.messages };
  }

  // 获取工作流ID（从状态或生成一个）
  const workflowId = state.workflowId || `workflow_${Date.now()}`;

  // 初始化状态管理
  const riskState = riskManagerStateManager.initializeState(workflowId, {
    ticker: state.ticker,
    startedAt: new Date().toISOString(),
  });

  // 开始分析
  riskManagerStateManager.startAnalysis(workflowId);

  const startTime = Date.now();

  try {
    // 第一步：收集风险数据
    console.log('[Risk Manager] Collecting risk data...');
    riskManagerStateManager.updateTaskStatus(workflowId, 'data_collection', 'running', 10);

    let riskData: RiskDataCollection | null = null;
    const dataCollectionStart = Date.now();

    try {
      riskData = await riskDataCollector.collectRiskData(state.ticker);
      const dataCollectionTime = Date.now() - dataCollectionStart;

      riskManagerStateManager.updatePerformanceMetrics(workflowId, {
        dataCollectionTime,
      });

      riskManagerStateManager.updateTaskStatus(
        workflowId,
        'data_collection',
        'completed',
        100,
        riskData
      );
      console.log('[Risk Manager] Risk data collection completed');
    } catch (dataError) {
      riskManagerStateManager.updateTaskStatus(
        workflowId,
        'data_collection',
        'failed',
        0,
        null,
        `数据收集失败: ${dataError instanceof Error ? dataError.message : '未知错误'}`
      );
      console.warn(
        '[Risk Manager] Risk data collection failed, proceeding with available data:',
        dataError
      );
    }

    // 第二步：获取 OpenAI 配置
    riskManagerStateManager.updateTaskStatus(workflowId, 'market_risk_analysis', 'running', 20);

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.nuwaapi.com/v1';

    // 第三步：初始化 LLM
    const model = new ChatOpenAI({
      modelName: state.config?.deepThinkLLM || 'gpt-4o',
      temperature: 0.1,
      apiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 第四步：构建增强的风险分析提示词（包含收集的数据）
    riskManagerStateManager.updateTaskStatus(workflowId, 'liquidity_risk_analysis', 'running', 40);
    const riskAnalysisPrompt = buildAdvancedRiskAnalysisPrompt(state, riskData);

    // 第五步：调用 LLM 进行风险分析
    console.log('[Risk Manager] Performing AI-powered risk analysis...');
    riskManagerStateManager.updateTaskStatus(workflowId, 'credit_risk_analysis', 'running', 60);

    const analysisStart = Date.now();
    const response = await model.invoke([new HumanMessage(riskAnalysisPrompt)]);
    const analysisTime = Date.now() - analysisStart;

    riskManagerStateManager.updatePerformanceMetrics(workflowId, {
      analysisTime,
    });

    // 第六步：解析风险分析结果
    riskManagerStateManager.updateTaskStatus(
      workflowId,
      'operational_risk_analysis',
      'running',
      70
    );
    riskManagerStateManager.updateTaskStatus(workflowId, 'scenario_analysis', 'running', 80);

    const reportGenerationStart = Date.now();
    const riskAssessment = parseRiskAssessment(response.content.toString());

    riskManagerStateManager.updateTaskStatus(workflowId, 'risk_metrics_calculation', 'running', 85);
    riskManagerStateManager.updateTaskStatus(workflowId, 'control_recommendations', 'running', 90);

    // 第七步：增强风险评估结果（添加数据质量信息）
    if (riskData) {
      riskAssessment.data_quality = riskData.data_quality;
      riskAssessment.raw_data_summary = {
        market_data_available: !!riskData.market_data,
        financial_data_available: !!riskData.financial_data,
        liquidity_data_available: !!riskData.liquidity_data,
        macro_data_available: !!riskData.macro_data,
        overall_data_quality: riskData.data_quality.overall_quality_score,
      };
    }

    // 第八步：计算执行时间
    const executionTime = Date.now() - startTime;
    riskAssessment.execution_time_ms = executionTime;

    // 更新性能指标
    const reportGenerationTime = Date.now() - reportGenerationStart;
    riskManagerStateManager.updatePerformanceMetrics(workflowId, {
      reportGenerationTime,
      totalTime: executionTime,
    });

    // 完成所有任务
    riskManagerStateManager.updateTaskStatus(
      workflowId,
      'report_generation',
      'completed',
      100,
      riskAssessment
    );

    // 标记其他任务为已完成
    [
      'market_risk_analysis',
      'liquidity_risk_analysis',
      'credit_risk_analysis',
      'operational_risk_analysis',
      'scenario_analysis',
      'risk_metrics_calculation',
      'control_recommendations',
    ].forEach((taskId) => {
      riskManagerStateManager.updateTaskStatus(workflowId, taskId, 'completed', 100);
    });

    // 完成整个分析
    riskManagerStateManager.completeAnalysis(workflowId, riskAssessment);

    // 第九步：更新状态
    const updatedMessages = [
      ...state.messages,
      new AIMessage(`风险管理师完成全面风险评估：${riskAssessment.summary}`),
    ];

    console.log('[Risk Manager] Risk assessment completed successfully');

    return {
      messages: updatedMessages,
      risk: riskAssessment,
      currentStage: 'risk_assessment_completed',
      progress: Math.min(state.progress + 10, 95),
      workflowId, // 确保返回工作流ID
    };
  } catch (error) {
    console.error('[Risk Manager] Error during risk assessment:', error);

    // 标记分析失败
    riskManagerStateManager.failAnalysis(
      workflowId,
      error instanceof Error ? error : new Error('未知错误')
    );

    const executionTime = Date.now() - startTime;
    const errorMessage = `风险管理师分析失败: ${
      error instanceof Error ? error.message : '未知错误'
    }`;
    const updatedMessages = [...state.messages, new AIMessage(errorMessage)];

    return {
      messages: updatedMessages,
      risk: {
        overall_risk_level: 'high',
        risk_score: 8,
        summary: '由于系统错误，无法完成风险评估，建议谨慎投资',
        market_risk: { level: 'high', score: 8, factors: ['系统分析失败'] },
        liquidity_risk: { level: 'high', score: 8, factors: ['无法评估流动性'] },
        credit_risk: { level: 'high', score: 8, factors: ['无法评估信用状况'] },
        operational_risk: { level: 'high', score: 8, factors: ['系统风险'] },
        recommendations: ['暂停投资决策', '等待系统恢复后重新评估'],
        risk_controls: {
          position_size_limit: 0,
          stop_loss_percentage: 5,
          max_holding_period: '1天',
        },
        execution_time_ms: executionTime,
        status: 'failed',
      },
      currentStage: 'risk_assessment_failed',
      progress: state.progress,
      workflowId, // 确保返回工作流ID
    };
  }
}

/**
 * 构建高级风险分析提示词（包含收集的数据）
 */
function buildAdvancedRiskAnalysisPrompt(
  state: typeof TradingAgentAnnotation.State,
  riskData?: RiskDataCollection | null
): string {
  const { ticker, analysis, research, data } = state;

  let dataSection = '';
  if (riskData) {
    dataSection = `
## 专业风险数据分析

### 市场风险数据
- **当前价格**: ${riskData.market_data.current_price}
- **年化波动率**: ${(riskData.market_data.volatility_data.annualized_volatility * 100).toFixed(2)}%
- **Beta系数**: ${riskData.market_data.beta_data.beta_coefficient.toFixed(3)}
- **与市场相关性**: ${riskData.market_data.beta_data.correlation_with_market.toFixed(3)}
- **52周价格区间**: ${riskData.market_data.price_statistics.min_price_52w} - ${
      riskData.market_data.price_statistics.max_price_52w
    }
- **近期价格变化**: 1日${riskData.market_data.price_statistics.price_change_1d.toFixed(
      2
    )}%, 7日${riskData.market_data.price_statistics.price_change_7d.toFixed(
      2
    )}%, 30日${riskData.market_data.price_statistics.price_change_30d.toFixed(2)}%

### 财务风险数据
- **财务健康度评分**: ${riskData.financial_data.financial_health_score}/100
- **债务股权比**: ${riskData.financial_data.debt_metrics.debt_to_equity.toFixed(2)}
- **流动比率**: ${riskData.financial_data.debt_metrics.current_ratio.toFixed(2)}
- **ROE**: ${(riskData.financial_data.profitability_metrics.roe * 100).toFixed(2)}%
- **净利润率**: ${(riskData.financial_data.profitability_metrics.net_margin * 100).toFixed(2)}%
- **自由现金流**: ${riskData.financial_data.cash_flow_metrics.free_cash_flow}

### 流动性风险数据
- **平均日成交量**: ${riskData.liquidity_data.volume_data.average_daily_volume}
- **成交量波动率**: ${(riskData.liquidity_data.volume_data.volume_volatility * 100).toFixed(2)}%
- **买卖价差**: ${(riskData.liquidity_data.spread_data.spread_percentage * 100).toFixed(3)}%
- **Amihud流动性比率**: ${riskData.liquidity_data.liquidity_metrics.amihud_ratio}

### 宏观风险数据
- **市场Beta**: ${riskData.macro_data.market_indices.market_beta.toFixed(3)}
- **行业相关性**: ${riskData.macro_data.market_indices.sector_correlation.toFixed(3)}
- **利率敏感度**: ${riskData.macro_data.economic_indicators.interest_rate_sensitivity.toFixed(3)}

### 数据质量评估
- **整体数据质量**: ${riskData.data_quality.overall_quality_score.toFixed(1)}/100
- **数据完整性**: ${riskData.data_quality.completeness_score.toFixed(1)}%
- **数据时效性**: ${riskData.data_quality.timeliness_score.toFixed(1)}%
- **缺失数据字段**: ${riskData.data_quality.missing_data_fields.join(', ') || '无'}
`;
  } else {
    dataSection = `
## 风险数据收集状态
⚠️ **数据收集失败**: 无法获取详细的风险数据，分析将基于有限信息进行。建议在数据可用后重新评估。
`;
  }

  return `# 专业风险管理师分析任务

你是一位拥有15年经验的专业风险管理师，曾在顶级投资银行和对冲基金工作。你的任务是对股票投资进行全面、深度的风险评估，为投资决策提供关键的风险控制建议。

## 分析标的信息
- **股票代码**: ${ticker}
- **分析日期**: ${state.date}
- **分析时间**: ${new Date().toISOString()}

${dataSection}

## 前期分析团队报告综述

### 分析师团队报告
${
  analysis.fundamental
    ? `
**基本面分析师报告**
- 核心观点: ${analysis.fundamental.summary}
- 关键财务指标: ${JSON.stringify(analysis.fundamental.key_metrics || {}, null, 2)}
- 估值水平: ${analysis.fundamental.valuation || '待评估'}
- 财务健康度: ${analysis.fundamental.financial_health || '待评估'}
`
    : '⚠️ 基本面分析缺失'
}

${
  analysis.technical
    ? `
**技术分析师报告**
- 技术观点: ${analysis.technical.summary}
- 关键技术指标: ${JSON.stringify(analysis.technical.indicators || {}, null, 2)}
- 趋势方向: ${analysis.technical.trend || '待确定'}
- 支撑阻力位: ${analysis.technical.levels || '待确定'}
`
    : '⚠️ 技术分析缺失'
}

${
  analysis.sentiment
    ? `
**情绪分析师报告**
- 市场情绪: ${analysis.sentiment.summary}
- 情绪指标: ${JSON.stringify(analysis.sentiment.metrics || {}, null, 2)}
- 投资者心理: ${analysis.sentiment.psychology || '待评估'}
`
    : '⚠️ 情绪分析缺失'
}

${
  analysis.news
    ? `
**新闻分析师报告**
- 新闻影响: ${analysis.news.summary}
- 关键事件: ${analysis.news.key_events || '无重大事件'}
- 舆论趋势: ${analysis.news.trend || '中性'}
`
    : '⚠️ 新闻分析缺失'
}

### 研究团队观点对比
${
  research.bull
    ? `
**多头研究员观点**
- 核心论点: ${research.bull.summary}
- 置信度: ${research.bull.confidence || 'N/A'}
- 目标价位: ${research.bull.target_price || '未设定'}
- 关键驱动因素: ${research.bull.key_drivers || '待识别'}
`
    : '⚠️ 多头研究缺失'
}

${
  research.bear
    ? `
**空头研究员观点**
- 核心论点: ${research.bear.summary}
- 置信度: ${research.bear.confidence || 'N/A'}
- 风险价位: ${research.bear.risk_price || '未设定'}
- 主要风险点: ${research.bear.key_risks || '待识别'}
`
    : '⚠️ 空头研究缺失'
}

${
  research.consensus
    ? `
**团队共识评估**
- 共识方向: ${research.consensus.consensus_direction}
- 共识置信度: ${research.consensus.consensus_confidence}
- 综合判断: ${research.consensus.synthesis_summary}
- 关键分歧点: ${JSON.stringify(research.consensus.key_disagreement_points || [])}
`
    : '⚠️ 共识评估缺失'
}

## 专业风险评估要求

基于以上数据和分析，请按照以下专业框架进行全面风险评估：

### 1. 市场风险评估 (Market Risk)
- 基于实际波动率数据分析价格风险
- 评估Beta系数和系统性风险暴露
- 分析与市场和行业的相关性风险

### 2. 流动性风险评估 (Liquidity Risk)
- 基于成交量数据评估流动性充足性
- 分析买卖价差和市场深度
- 评估大额交易的市场冲击风险

### 3. 信用风险评估 (Credit Risk)
- 基于财务数据评估偿债能力
- 分析债务结构和杠杆水平
- 评估现金流稳定性和财务健康度

### 4. 操作风险评估 (Operational Risk)
- 分析公司治理和管理层风险
- 评估监管环境和合规风险
- 识别业务模式和竞争风险

### 5. 综合情景分析
- 设计熊市、基准、牛市情景
- 基于历史数据和当前环境评估概率
- 计算各情景下的风险收益比

请严格按照以下JSON格式返回专业风险评估结果：

\`\`\`json
{
  "overall_risk_level": "low|medium|high",
  "risk_score": 1-10,
  "summary": "基于数据分析的整体风险评估摘要，包含关键发现和建议",
  "market_risk": {
    "level": "low|medium|high",
    "score": 1-10,
    "factors": ["基于实际数据识别的市场风险因素"],
    "volatility_analysis": "基于历史波动率的详细分析",
    "beta_assessment": "Beta系数和系统性风险的专业评估",
    "correlation_analysis": "相关性风险的量化分析",
    "macro_sensitivity": "宏观经济敏感度的评估"
  },
  "liquidity_risk": {
    "level": "low|medium|high",
    "score": 1-10,
    "factors": ["基于成交量数据的流动性风险因素"],
    "volume_analysis": "成交量趋势和异常情况分析",
    "spread_assessment": "买卖价差和市场深度的专业评估",
    "impact_cost": "基于Amihud比率的市场冲击成本分析",
    "liquidity_triggers": "可能导致流动性问题的具体触发因素"
  },
  "credit_risk": {
    "level": "low|medium|high",
    "score": 1-10,
    "factors": ["基于财务数据的信用风险因素"],
    "financial_health": "基于财务健康度评分的详细分析",
    "debt_analysis": "债务结构和偿债能力的专业评估",
    "cash_flow_stability": "现金流稳定性和可持续性分析",
    "credit_rating_outlook": "信用状况的前瞻性评估"
  },
  "operational_risk": {
    "level": "low|medium|high",
    "score": 1-10,
    "factors": ["识别的操作风险因素"],
    "management_assessment": "管理层能力和稳定性评估",
    "governance_analysis": "公司治理结构分析",
    "regulatory_risk": "监管环境变化风险评估",
    "business_model_risk": "商业模式和竞争环境风险"
  },
  "scenario_analysis": {
    "bear_case": {
      "probability": "基于数据分析的熊市概率",
      "potential_loss": "基于波动率的潜在损失估算",
      "triggers": ["具体的风险触发因素"],
      "timeline": "预期时间框架",
      "recovery_outlook": "恢复前景评估"
    },
    "base_case": {
      "probability": "基准情景概率",
      "expected_return": "基于历史数据的预期收益",
      "key_assumptions": ["关键假设条件"],
      "risk_factors": ["主要风险因素"]
    },
    "bull_case": {
      "probability": "牛市情景概率",
      "potential_gain": "潜在收益估算",
      "catalysts": ["推动因素"],
      "sustainability": "收益可持续性评估"
    }
  },
  "risk_metrics": {
    "var_95": "基于历史数据的95%置信度VaR",
    "var_99": "99%置信度VaR",
    "expected_shortfall": "预期损失估算",
    "max_drawdown": "基于历史数据的最大回撤分析",
    "sharpe_ratio_estimate": "风险调整收益比估算",
    "sortino_ratio": "下行风险调整收益比",
    "correlation_risk": "相关性和集中度风险评估",
    "tail_risk": "尾部风险和极端事件分析"
  },
  "recommendations": [
    "基于数据分析的具体投资建议",
    "量化的风险控制措施",
    "基于市场环境的时机建议",
    "投资策略和持有期建议"
  ],
  "risk_controls": {
    "position_size_limit": "基于风险评估的仓位上限（数值）",
    "stop_loss_percentage": "基于波动率的止损建议（数值）",
    "take_profit_percentage": "基于风险收益比的止盈建议（数值）",
    "max_holding_period": "基于流动性分析的持有期建议",
    "rebalancing_frequency": "仓位调整频率建议",
    "monitoring_indicators": ["需要监控的关键风险指标"],
    "trigger_conditions": ["需要立即行动的量化触发条件"]
  },
  "risk_warnings": [
    "基于数据分析的重要风险提示",
    "市场环境变化的预警信号",
    "数据质量相关的注意事项",
    "需要特别关注的风险点"
  ],
  "professional_opinion": {
    "risk_appetite_suitability": "基于风险评估的投资者适合性分析",
    "portfolio_allocation": "在投资组合中的建议配置比例",
    "hedging_suggestions": "基于相关性分析的对冲建议",
    "exit_strategy": "基于风险指标的退出策略"
  }
}
\`\`\`

## 专业要求
1. **数据驱动**: 充分利用提供的量化数据进行分析
2. **客观性**: 基于事实和数据，避免主观判断
3. **量化性**: 提供具体的数值和区间，而非模糊描述
4. **可操作性**: 提供具体可执行的风险控制措施
5. **专业性**: 体现资深风险管理师的专业水准

请确保你的分析充分利用了提供的风险数据，并体现出专业的风险管理判断。`;
}

/**
 * 解析风险分析结果
 */
function parseRiskAssessment(content: string): any {
  try {
    // 尝试从响应中提取JSON
    const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[1]);
      // 确保包含状态字段
      parsed.status = 'completed';
      return parsed;
    }

    // 如果没有找到JSON格式，尝试直接解析
    const parsed = JSON.parse(content);
    parsed.status = 'completed';
    return parsed;
  } catch (error) {
    console.warn('[Risk Manager] Failed to parse risk assessment, using enhanced fallback');

    // 返回增强的默认风险评估结构
    return {
      overall_risk_level: 'medium',
      risk_score: 5,
      summary: '由于解析错误，提供默认风险评估。建议人工审核并重新分析。',
      market_risk: {
        level: 'medium',
        score: 5,
        factors: ['解析错误，无法获取具体市场风险因素'],
        volatility_analysis: '需要人工分析历史波动率和预期波动区间',
        beta_assessment: '需要人工评估Beta系数和系统性风险',
        correlation_analysis: '需要分析与市场和行业的相关性',
        macro_sensitivity: '需要评估宏观经济敏感度',
      },
      liquidity_risk: {
        level: 'medium',
        score: 5,
        factors: ['解析错误，无法获取具体流动性风险因素'],
        volume_analysis: '需要人工分析成交量趋势',
        spread_assessment: '需要人工评估买卖价差和市场深度',
        impact_cost: '需要估算大额交易的市场冲击成本',
        liquidity_triggers: '需要识别流动性枯竭的触发因素',
      },
      credit_risk: {
        level: 'medium',
        score: 5,
        factors: ['解析错误，无法获取具体信用风险因素'],
        financial_health: '需要人工评估财务健康度和关键比率',
        debt_analysis: '需要人工分析债务结构和偿债能力',
        cash_flow_stability: '需要分析现金流稳定性',
        credit_rating_outlook: '需要评估信用评级展望',
      },
      operational_risk: {
        level: 'medium',
        score: 5,
        factors: ['解析错误，无法获取具体操作风险因素'],
        management_assessment: '需要人工评估管理层能力和稳定性',
        governance_analysis: '需要人工分析公司治理结构',
        regulatory_risk: '需要评估监管环境变化风险',
        business_model_risk: '需要分析商业模式和竞争环境风险',
      },
      scenario_analysis: {
        bear_case: {
          probability: '30%',
          potential_loss: '20%',
          triggers: ['市场下跌', '行业风险'],
          timeline: '3-6个月',
          recovery_outlook: '需要评估',
        },
        base_case: {
          probability: '40%',
          expected_return: '5%',
          key_assumptions: ['市场稳定', '基本面不变'],
          risk_factors: ['市场波动', '行业变化'],
        },
        bull_case: {
          probability: '30%',
          potential_gain: '15%',
          catalysts: ['积极因素', '市场上涨'],
          sustainability: '需要评估',
        },
      },
      risk_metrics: {
        var_95: '需要计算95%置信度VaR',
        var_99: '需要计算99%置信度VaR',
        expected_shortfall: '需要计算预期损失',
        max_drawdown: '需要计算最大回撤',
        sharpe_ratio_estimate: '需要估算夏普比率',
        sortino_ratio: '需要计算索提诺比率',
        correlation_risk: '需要评估相关性风险',
        tail_risk: '需要评估尾部风险',
      },
      recommendations: [
        '建议人工审核风险评估结果',
        '谨慎控制仓位规模',
        '设置合理的止损位',
        '密切监控市场变化',
        '考虑分散投资策略',
      ],
      risk_controls: {
        position_size_limit: 10,
        stop_loss_percentage: 8,
        take_profit_percentage: 15,
        max_holding_period: '30天',
        rebalancing_frequency: '每周评估',
        monitoring_indicators: ['价格变动', '成交量变化', '新闻事件', '技术指标'],
        trigger_conditions: ['跌破止损位', '重大负面消息', '流动性枯竭'],
      },
      risk_warnings: [
        '风险评估解析失败，请人工审核',
        '建议降低投资规模直到完成人工审核',
        '密切关注市场环境变化',
        '及时调整风险控制参数',
      ],
      professional_opinion: {
        risk_appetite_suitability: '适合中等风险偏好投资者',
        portfolio_allocation: '建议配置比例不超过5%',
        hedging_suggestions: '考虑使用期权或其他衍生品对冲',
        exit_strategy: '设定明确的退出条件和时机',
      },
      status: 'completed',
    };
  }
}

/**
 * 计算风险指标的辅助函数
 */
export function calculateRiskMetrics(data: any): any {
  // 这里可以添加实际的风险指标计算逻辑
  // 例如：VaR计算、波动率计算、相关性分析等
  return {
    calculated: true,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 风险等级映射函数
 */
export function mapRiskLevel(score: number): 'low' | 'medium' | 'high' {
  if (score <= 3) return 'low';
  if (score <= 7) return 'medium';
  return 'high';
}
