'use client';

import { ViewColumnsIcon, Squares2X2Icon } from '@heroicons/react/24/outline';

export type ViewType = 'grid' | 'table';

interface ViewToggleProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  className?: string;
}

export function ViewToggle({ currentView, onViewChange, className = '' }: ViewToggleProps) {
  return (
    <div className={`inline-flex rounded-lg border border-slate-200 dark:border-slate-600 bg-white dark:bg-slate-800 p-1 ${className}`}>
      <button
        onClick={() => onViewChange('grid')}
        className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
          currentView === 'grid'
            ? 'bg-blue-600 text-white shadow-sm'
            : 'text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-50 dark:hover:bg-slate-700'
        }`}
        aria-label="卡片视图"
        title="卡片视图"
      >
        <Squares2X2Icon className="h-4 w-4 mr-1.5" />
        卡片
      </button>
      <button
        onClick={() => onViewChange('table')}
        className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
          currentView === 'table'
            ? 'bg-blue-600 text-white shadow-sm'
            : 'text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-50 dark:hover:bg-slate-700'
        }`}
        aria-label="表格视图"
        title="表格视图"
      >
        <ViewColumnsIcon className="h-4 w-4 mr-1.5" />
        表格
      </button>
    </div>
  );
}