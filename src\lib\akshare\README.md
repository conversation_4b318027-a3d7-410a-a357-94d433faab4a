# AKShare 适配器使用指南

## 概述

AKShare 适配器是与后端 AKShare 服务交互的核心组件，支持工作流追踪和数据记录功能。

## 基本使用

```typescript
import { akshareAdapter } from '@/lib/akshare/adapter';

// 基本使用
const stockData = await akshareAdapter.getStockHistory({
  symbol: 'AAPL',
  period: 'daily'
});
```

## 工作流ID功能

### 1. 设置全局工作流ID

```typescript
// 设置当前工作流ID，后续所有API调用都会包含此ID
akshareAdapter.setWorkflowId('workflow_123');

// 获取股票数据，会自动包含工作流ID
const stockData = await akshareAdapter.getStockHistory({
  symbol: 'AAPL',
  period: 'daily'
});

// 清除工作流ID
akshareAdapter.setWorkflowId(null);
```

### 2. 单次调用传递工作流ID

```typescript
// 在单个方法调用中传递工作流ID
const stockData = await akshareAdapter.getStockHistory({
  symbol: 'AAPL',
  period: 'daily',
  workflow_id: 'workflow_123'
});

const newsData = await akshareAdapter.getStockNews({
  symbol: 'AAPL',
  limit: 10,
  workflow_id: 'workflow_123'
});
```

### 3. 使用工作流上下文

```typescript
// 在指定工作流上下文中执行多个操作
const result = await akshareAdapter.withWorkflowId('workflow_123', async () => {
  const stockData = await akshareAdapter.getStockHistory({
    symbol: 'AAPL',
    period: 'daily'
  });
  
  const newsData = await akshareAdapter.getStockNews({
    symbol: 'AAPL',
    limit: 10
  });
  
  return { stockData, newsData };
});
```

## API 方法

### 股票数据

```typescript
// 获取股票历史数据
const history = await akshareAdapter.getStockHistory({
  symbol: 'AAPL',
  period: 'daily',
  start_date: '20240101',
  end_date: '20241231',
  workflow_id: 'optional_workflow_id'
});

// 获取股票实时数据
const realtime = await akshareAdapter.getStockRealtime('AAPL', 'workflow_id');

// 获取当前价格
const price = await akshareAdapter.getCurrentPrice('AAPL', 'workflow_id');
```

### 新闻数据

```typescript
// 获取股票新闻
const news = await akshareAdapter.getStockNews({
  symbol: 'AAPL',
  limit: 20,
  workflow_id: 'workflow_123'
});
```

### 基本面数据

```typescript
// 获取基本面数据
const fundamental = await akshareAdapter.getFinancialData({
  symbol: 'AAPL',
  indicator: 'balance_sheet',
  period_type: 'quarterly',
  time_period: '2024Q1',
  workflow_id: 'workflow_123'
});
```

### 技术指标

```typescript
// 获取技术指标
const technical = await akshareAdapter.getTechnicalIndicators({
  symbol: 'AAPL',
  indicator: 'RSI',
  period: '14',
  workflow_id: 'workflow_123'
});
```

## 工作流集成示例

### LangGraph 工作流中的使用

```typescript
// 在 LangGraph 工作流中使用
export async function dataCollectionNode(state: WorkflowState) {
  const { workflow_id, ticker } = state;
  
  // 设置工作流ID
  akshareAdapter.setWorkflowId(workflow_id);
  
  try {
    // 收集各种数据
    const [stockData, newsData, fundamentalData, technicalData] = await Promise.all([
      akshareAdapter.getStockHistory({ symbol: ticker, period: 'daily' }),
      akshareAdapter.getStockNews({ symbol: ticker, limit: 20 }),
      akshareAdapter.getFinancialData({ symbol: ticker }),
      akshareAdapter.getTechnicalIndicators({ symbol: ticker, indicator: 'RSI' })
    ]);
    
    return {
      ...state,
      stockData,
      newsData,
      fundamentalData,
      technicalData
    };
  } finally {
    // 清理工作流ID（可选）
    akshareAdapter.setWorkflowId(null);
  }
}
```

### React 组件中的使用

```typescript
import { useEffect, useState } from 'react';
import { akshareAdapter } from '@/lib/akshare/adapter';

export function StockAnalysis({ workflowId, ticker }: { workflowId: string; ticker: string }) {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    const fetchData = async () => {
      // 使用工作流上下文
      const result = await akshareAdapter.withWorkflowId(workflowId, async () => {
        return {
          price: await akshareAdapter.getCurrentPrice(ticker),
          news: await akshareAdapter.getStockNews({ symbol: ticker, limit: 5 }),
          technical: await akshareAdapter.getTechnicalIndicators({ 
            symbol: ticker, 
            indicator: 'RSI' 
          })
        };
      });
      
      setData(result);
    };
    
    fetchData();
  }, [workflowId, ticker]);
  
  return <div>{/* 渲染数据 */}</div>;
}
```

## 错误处理

```typescript
try {
  const data = await akshareAdapter.getStockHistory({
    symbol: 'INVALID_SYMBOL',
    workflow_id: 'workflow_123'
  });
} catch (error) {
  console.error('获取股票数据失败:', error.message);
  // 错误会包含工作流ID信息，便于调试和追踪
}
```

## 健康检查

```typescript
// 检查后端服务状态
const health = await akshareAdapter.healthCheck();
console.log('服务状态:', health.status);
console.log('延迟:', health.latency, 'ms');
```

## 批量操作

```typescript
// 批量获取数据
const requests = [
  { command: 'get_stock_history', params: { symbol: 'AAPL', workflow_id: 'wf_123' } },
  { command: 'get_stock_news', params: { symbol: 'AAPL', workflow_id: 'wf_123' } },
  { command: 'get_financial_data', params: { symbol: 'AAPL', workflow_id: 'wf_123' } }
];

const results = await akshareAdapter.getBatchData(requests);
```

## 最佳实践

1. **工作流ID管理**
   - 在工作流开始时设置工作流ID
   - 使用 `withWorkflowId()` 确保上下文正确
   - 在工作流结束时清理工作流ID

2. **错误处理**
   - 始终使用 try-catch 包装API调用
   - 记录包含工作流ID的错误信息

3. **性能优化**
   - 使用批量操作减少网络请求
   - 合理设置超时时间
   - 利用后端缓存机制

4. **数据追踪**
   - 确保所有数据请求都包含工作流ID
   - 利用工作流ID进行数据关联和分析
   - 在数据库中查询特定工作流的所有数据操作