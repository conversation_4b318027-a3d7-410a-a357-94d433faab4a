#!/usr/bin/env python3
"""
快速检查 AKShare 服务状态
"""

import requests
import sys

def quick_check():
    """快速检查服务状态"""
    urls = [
        "http://localhost:5000",
        "http://127.0.0.1:5000"
    ]
    
    for url in urls:
        print(f"检查 {url}...")
        
        try:
            # 测试根路径
            response = requests.get(f"{url}/", timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务运行正常: {data.get('service', 'AKShare')} v{data.get('version', '1.0')}")
                
                # 测试健康检查
                health_response = requests.get(f"{url}/health", timeout=3)
                if health_response.status_code == 200:
                    health_data = health_response.json()
                    print(f"✅ 健康检查通过: {health_data.get('status', 'unknown')}")
                else:
                    print(f"❌ 健康检查失败: {health_response.status_code}")
                
                return True
            else:
                print(f"❌ 服务响应异常: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 无法连接到服务")
        except requests.exceptions.Timeout:
            print(f"⏰ 连接超时")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n💡 服务可能未启动，请运行:")
    print("   cd backend/akshare-service")
    print("   python app/main.py")
    print("   或")
    print("   python start_service.py")
    
    return False

if __name__ == "__main__":
    if quick_check():
        print("\n🎉 AKShare 服务运行正常!")
        sys.exit(0)
    else:
        print("\n❌ AKShare 服务未正常运行!")
        sys.exit(1)