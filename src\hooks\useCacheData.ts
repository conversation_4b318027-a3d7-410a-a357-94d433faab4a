/**
 * 缓存数据 React Hooks
 * 提供便捷的缓存数据获取和状态管理
 */

import { useState, useEffect, useCallback } from 'react';
import {
  cacheDataService,
  type CachedFundamentalDataParams,
  type CachedNewsDataParams,
  type CachedFundamentalData,
  type CachedNewsData
} from '@/lib/cache-data-service';

// Hook状态类型
interface CacheDataState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Hook返回类型
interface CacheDataHookReturn<T> extends CacheDataState<T> {
  refetch: () => Promise<void>;
  reset: () => void;
}

/**
 * 缓存基本面数据Hook
 */
export function useCachedFundamentalData(
  params: CachedFundamentalDataParams,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
  }
) {
  const { enabled = true, refetchInterval } = options || {};

  const [state, setState] = useState<CacheDataState<CachedFundamentalData[]>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !params.symbol) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await cacheDataService.getCachedFundamentalData(params);
      
      if (result.success && result.data) {
        setState({
          data: result.data,
          loading: false,
          error: null,
          lastUpdated: new Date(),
        });
      } else {
        setState({
          data: null,
          loading: false,
          error: result.message || '未找到缓存的基本面数据',
          lastUpdated: null,
        });
      }
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取缓存基本面数据失败',
        lastUpdated: null,
      });
    }
  }, [params.symbol, params.indicator, params.period_type, params.time_period, params.limit, enabled]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 缓存新闻数据Hook
 */
export function useCachedNewsData(
  params: CachedNewsDataParams,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
  }
) {
  const { enabled = true, refetchInterval } = options || {};

  const [state, setState] = useState<CacheDataState<CachedNewsData[]>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !params.symbol) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await cacheDataService.getCachedNewsData(params);
      
      if (result.success && result.data) {
        setState({
          data: result.data,
          loading: false,
          error: null,
          lastUpdated: new Date(),
        });
      } else {
        setState({
          data: null,
          loading: false,
          error: result.message || '未找到缓存的新闻数据',
          lastUpdated: null,
        });
      }
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取缓存新闻数据失败',
        lastUpdated: null,
      });
    }
  }, [params.symbol, params.limit, enabled]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 缓存数据检查Hook
 */
export function useCacheCheck(
  dataType: 'fundamental' | 'news',
  params: { symbol: string; [key: string]: any },
  options?: {
    enabled?: boolean;
  }
) {
  const { enabled = true } = options || {};
  const [exists, setExists] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const checkCache = useCallback(async () => {
    if (!enabled) return;

    setLoading(true);
    try {
      const result = await cacheDataService.checkCacheExists(dataType, params);
      setExists(result);
    } catch (error) {
      console.error('检查缓存失败:', error);
    } finally {
      setLoading(false);
    }
  }, [dataType, params, enabled]);

  useEffect(() => {
    checkCache();
  }, [checkCache]);

  return {
    exists,
    loading,
    checkCache,
  };
}