import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { dbConfig } from '@/lib/db-config';

/**
 * 清理过期缓存数据
 * POST /api/cache/cleanup
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { days_old = 1 } = body;

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 删除过期的基本面数据缓存
      const [fundamentalResult]: any = await connection.execute(
        'DELETE FROM stock_fundamental_data WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
        [days_old]
      );
      
      // 删除过期的新闻数据缓存
      const [newsResult]: any = await connection.execute(
        'DELETE FROM financial_news WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
        [days_old]
      );

      await connection.end();

      const deletedRecords = fundamentalResult.affectedRows + newsResult.affectedRows;

      return NextResponse.json({
        success: true,
        data: {
          deleted_records: deletedRecords,
          fundamental_deleted: fundamentalResult.affectedRows,
          news_deleted: newsResult.affectedRows
        }
      });

    } catch (queryError) {
      await connection.end();
      throw queryError;
    }

  } catch (error) {
    console.error('[清理缓存数据API] 错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}