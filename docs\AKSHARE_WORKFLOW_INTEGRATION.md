# AKShare 工作流集成指南

## 概述

本文档介绍如何使用 AKShare 数据服务的工作流绑定功能，实现数据获取与工作流的完整追踪和记录。

## 功能特性

### 1. 工作流数据绑定
- 所有数据请求都可以关联到特定的工作流ID
- 自动记录数据获取的详细信息（执行时间、参数、结果等）
- 支持错误追踪和性能分析

### 2. 数据缓存优化
- 股票历史数据缓存到 `stock_daily_data` 表
- 新闻数据缓存到 `financial_news` 表
- 基本面数据缓存到 `stock_fundamental_data` 表

### 3. 完整的日志记录
- 工作流事件记录到 `workflow_events` 表
- 数据获取详情记录到 `data_fetch_logs` 表
- 支持执行时间、错误信息、参数记录

## 使用方法

### 前端调用示例

```typescript
import { akshareAdapter } from '@/lib/akshare/adapter';

// 方法1: 设置全局工作流ID
akshareAdapter.setWorkflowId('workflow_123');
const stockData = await akshareAdapter.getStockHistory({
  symbol: 'AAPL',
  period: 'daily'
});

// 方法2: 单次调用传递工作流ID
const newsData = await akshareAdapter.getStockNews({
  symbol: 'AAPL',
  limit: 10,
  workflow_id: 'workflow_123'
});

// 方法3: 使用工作流上下文
const result = await akshareAdapter.withWorkflowId('workflow_123', async () => {
  const [stock, news, fundamental] = await Promise.all([
    akshareAdapter.getStockHistory({ symbol: 'AAPL' }),
    akshareAdapter.getStockNews({ symbol: 'AAPL', limit: 5 }),
    akshareAdapter.getFinancialData({ symbol: 'AAPL' })
  ]);
  return { stock, news, fundamental };
});
```

### 后端API接口

所有接口都支持 `workflow_id` 参数：

```bash
# 股票历史数据
curl -X POST http://localhost:5000/api/stock/history \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "period": "daily",
    "time_period": "1m",
    "workflow_id": "workflow_123"
  }'

# 股票新闻
curl -X POST http://localhost:5000/api/stock/news \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "limit": 10,
    "workflow_id": "workflow_123"
  }'

# 基本面数据
curl -X POST http://localhost:5000/api/stock/fundamental \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "indicator": "all",
    "period_type": "按报告期",
    "workflow_id": "workflow_123"
  }'

# 实时数据
curl -X POST http://localhost:5000/api/stock/realtime \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "workflow_id": "workflow_123"
  }'

# 技术指标
curl -X POST http://localhost:5000/api/stock/technical \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "indicator": "RSI",
    "period": "14",
    "workflow_id": "workflow_123"
  }'
```

## 数据库查询

### 查看工作流数据获取统计

```sql
-- 查看所有工作流的数据获取概览
SELECT * FROM workflow_data_stats;

-- 查看特定工作流的详细统计
CALL GetWorkflowDataStats('workflow_123');

-- 查看工作流的所有事件
SELECT * FROM workflow_events 
WHERE workflow_id = 'workflow_123' 
ORDER BY created_at DESC;

-- 查看工作流的数据获取日志
SELECT * FROM data_fetch_logs 
WHERE workflow_id = 'workflow_123' 
ORDER BY created_at DESC;
```

### 性能分析查询

```sql
-- 查看各数据类型的平均执行时间
SELECT 
    data_type,
    COUNT(*) as total_requests,
    AVG(execution_time_ms) as avg_time_ms,
    MIN(execution_time_ms) as min_time_ms,
    MAX(execution_time_ms) as max_time_ms,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
FROM data_fetch_logs 
GROUP BY data_type
ORDER BY avg_time_ms DESC;

-- 查看最近的错误记录
SELECT 
    workflow_id,
    data_type,
    symbol,
    error_message,
    created_at
FROM data_fetch_logs 
WHERE status = 'failed' 
ORDER BY created_at DESC 
LIMIT 10;
```

## 部署步骤

### 1. 更新数据库

```bash
# 方法1: 使用完整初始化脚本（新部署）
mysql -u root -p < database/init.sql

# 方法2: 使用迁移脚本（现有数据库）
mysql -u root -p < database/migrations/add-akshare-tables.sql
```

### 2. 启动后端服务

```bash
cd backend/akshare-service
pip install -r requirements.txt
python app/main.py
```

### 3. 测试功能

```bash
# 运行测试脚本
cd backend/akshare-service
python test_workflow_binding.py
```

### 4. 验证数据库记录

```sql
-- 检查测试数据
SELECT * FROM workflow_events WHERE workflow_id LIKE 'test_workflow_%';
SELECT * FROM data_fetch_logs WHERE workflow_id LIKE 'test_workflow_%';
```

## 配置说明

### 环境变量

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=trading_analysis

# 后端服务配置
BACK_END_URL=http://localhost:5000
```

### 前端配置

确保前端的 AKShare 适配器配置正确：

```typescript
// src/lib/akshare/adapter.ts
const adapter = new AKShareAdapter();
adapter.setWorkflowId('your_workflow_id');
```

## 监控和维护

### 1. 性能监控

- 监控 `data_fetch_logs` 表中的执行时间
- 关注失败率和错误模式
- 定期清理过期的缓存数据

### 2. 数据清理

```sql
-- 清理30天前的数据获取日志
DELETE FROM data_fetch_logs 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理过期的缓存数据
DELETE FROM stock_daily_data 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 3. 索引优化

```sql
-- 检查索引使用情况
SHOW INDEX FROM data_fetch_logs;
SHOW INDEX FROM workflow_events;

-- 根据查询模式添加复合索引
CREATE INDEX idx_workflow_datatype_time 
ON data_fetch_logs(workflow_id, data_type, created_at);
```

## 故障排除

### 常见问题

1. **工作流ID未记录**
   - 检查请求是否包含 `workflow_id` 参数
   - 验证数据库连接是否正常

2. **数据获取失败**
   - 查看 `data_fetch_logs` 表中的错误信息
   - 检查 AKShare 服务状态

3. **性能问题**
   - 分析执行时间统计
   - 考虑增加缓存策略

### 调试命令

```bash
# 检查服务健康状态
curl http://localhost:5000/health

# 查看服务日志
tail -f backend/akshare-service/logs/app.log

# 测试数据库连接
mysql -u root -p trading_analysis -e "SELECT COUNT(*) FROM workflows;"
```

## 最佳实践

1. **工作流ID管理**
   - 使用UUID格式的工作流ID
   - 在工作流开始时设置ID，结束时清理

2. **错误处理**
   - 始终检查API响应状态
   - 记录详细的错误信息用于调试

3. **性能优化**
   - 合理使用缓存机制
   - 批量处理数据请求
   - 定期清理历史数据

4. **监控告警**
   - 设置失败率告警
   - 监控执行时间异常
   - 跟踪数据库存储使用情况