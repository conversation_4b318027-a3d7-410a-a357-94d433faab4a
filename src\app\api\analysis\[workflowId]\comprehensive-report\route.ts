import { analysisService } from '@/lib/analysis-service';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ workflowId: string }> }
) {
    try {
        const { workflowId } = await params;

        if (!workflowId) {
            return NextResponse.json(
                {
                    success: false,
                    error: '工作流ID不能为空',
                    timestamp: new Date().toISOString(),
                },
                { status: 400 }
            );
        }

        // Get comprehensive analysis status which includes all reports
        const analysisStatus = await analysisService.getAnalysisStatus(workflowId);

        // Generate comprehensive report
        const comprehensiveReport = {
            workflowId: analysisStatus.workflowId,
            ticker: analysisStatus.ticker,
            title: analysisStatus.title,
            status: analysisStatus.status,
            progress: analysisStatus.progress,
            currentStage: analysisStatus.currentStage,
            createdAt: analysisStatus.createdAt,
            completedAt: analysisStatus.completedAt,

            // Analysis sections
            analystReports: analysisStatus.analystReports,
            researchReports: analysisStatus.researchReports,
            finalDecision: analysisStatus.finalDecision,
            recentEvents: analysisStatus.recentEvents,

            // Summary statistics
            summary: {
                totalAnalysts: analysisStatus.analystReports.length,
                completedAnalysts: analysisStatus.analystReports.filter(r => r.status === 'completed').length,
                totalResearchers: analysisStatus.researchReports.length,
                completedResearchers: analysisStatus.researchReports.filter(r => r.status === 'completed').length,
                hasDecision: !!analysisStatus.finalDecision,
                totalEvents: analysisStatus.recentEvents.length,
            }
        };

        return NextResponse.json({
            success: true,
            data: comprehensiveReport,
            timestamp: new Date().toISOString(),
        });
    } catch (error: any) {
        console.error('Get comprehensive report error:', error);

        // Handle specific error types
        if (error.code === 'ANALYSIS_NOT_FOUND') {
            return NextResponse.json(
                {
                    success: false,
                    error: '分析不存在',
                    timestamp: new Date().toISOString(),
                },
                { status: 404 }
            );
        }

        return NextResponse.json(
            {
                success: false,
                error: error.message || '获取综合报告失败',
                timestamp: new Date().toISOString(),
            },
            { status: 500 }
        );
    }
}