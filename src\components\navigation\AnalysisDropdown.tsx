'use client';

// import { useNavigationState } from '@/hooks/useNavigationState';
import { ChartBarIcon, ClockIcon, ScaleIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

interface AnalysisDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
  className?: string;
}

export function AnalysisDropdown({
  isOpen,
  onToggle,
  onClose,
  className = '',
}: AnalysisDropdownProps) {
  const router = useRouter();
  const dropdownRef = useRef<HTMLDivElement>(null);
  // Temporarily disable navigation state to fix the rendering issue
  // const { analysisStats, recentAnalyses, loading } = useNavigationState();
  const analysisStats = {
    total: 0,
    completed: 0,
    running: 0,
    todayCount: 0,
  };
  const recentAnalyses: any[] = [];
  const loading = false;
  const [focusedIndex, setFocusedIndex] = useState(-1);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    function handleKeyDown(event: KeyboardEvent) {
      const menuItems = dropdownRef.current?.querySelectorAll('[role="menuitem"]');
      if (!menuItems) return;

      switch (event.key) {
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
        case 'ArrowDown':
          event.preventDefault();
          setFocusedIndex((prev) => (prev + 1) % menuItems.length);
          break;
        case 'ArrowUp':
          event.preventDefault();
          setFocusedIndex((prev) => (prev - 1 + menuItems.length) % menuItems.length);
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          const focusedItem = menuItems[focusedIndex] as HTMLElement;
          if (focusedItem) {
            focusedItem.click();
          }
          break;
        case 'Tab':
          onClose();
          break;
      }
    }

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedIndex, onClose]);

  // Focus management
  useEffect(() => {
    if (isOpen && focusedIndex >= 0) {
      const menuItems = dropdownRef.current?.querySelectorAll('[role="menuitem"]');
      if (menuItems && menuItems[focusedIndex]) {
        (menuItems[focusedIndex] as HTMLElement).focus();
      }
    }
  }, [focusedIndex, isOpen]);

  const handleNavigation = (path: string) => {
    router.push(path);
    onClose();
  };

  const menuItems = [
    {
      name: '分析历史',
      href: '/tasks',
      icon: ClockIcon,
      description: '查看所有历史分析记录',
      shortcut: 'Alt+H',
    },
    {
      name: '分析对比',
      href: '/analysis/compare',
      icon: ScaleIcon,
      description: '对比多个分析结果',
      shortcut: 'Alt+C',
    },
  ];

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className={`absolute right-0 mt-2 w-80 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 py-2 z-50 ${className}`}
      role="menu"
      aria-label="分析功能菜单"
      aria-expanded={isOpen}
    >
      {/* Statistics Section */}
      {/* {analysisStats && (
        <div className="px-4 py-3 border-b border-slate-200 dark:border-slate-700">
          <h3 className="text-sm font-medium text-slate-900 dark:text-white mb-2">分析统计</h3>
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {analysisStats.total}
              </div>
              <div className="text-slate-600 dark:text-slate-400">总分析数</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                {analysisStats.completed}
              </div>
              <div className="text-slate-600 dark:text-slate-400">已完成</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                {analysisStats.running}
              </div>
              <div className="text-slate-600 dark:text-slate-400">进行中</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-purple-600 dark:text-purple-400">
                {analysisStats.todayCount}
              </div>
              <div className="text-slate-600 dark:text-slate-400">今日分析</div>
            </div>
          </div>
        </div>
      )} */}

      {/* Main Menu Items */}
      <div className="py-1">
        {menuItems.map((item, index) => {
          const Icon = item.icon;
          return (
            <button
              key={item.name}
              role="menuitem"
              tabIndex={-1}
              onClick={() => handleNavigation(item.href)}
              className="w-full flex items-center px-4 py-3 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 focus:bg-slate-100 dark:focus:bg-slate-700 focus:outline-none transition-colors"
              aria-describedby={`${item.name.toLowerCase().replace(' ', '-')}-desc`}
            >
              <Icon className="h-5 w-5 mr-3 text-slate-500 dark:text-slate-400" />
              <div className="flex-1 text-left">
                <div className="font-medium">{item.name}</div>
                <div className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">
                  {item.description}
                </div>
              </div>
              <div className="text-xs text-slate-400 dark:text-slate-500 ml-2">{item.shortcut}</div>
            </button>
          );
        })}
      </div>

      {/* Recent Analyses Section */}
      {recentAnalyses && recentAnalyses.length > 0 && (
        <div className="border-t border-slate-200 dark:border-slate-700 pt-2">
          <div className="px-4 py-2">
            <h3 className="text-sm font-medium text-slate-900 dark:text-white">最近分析</h3>
          </div>
          <div className="max-h-32 overflow-y-auto">
            {recentAnalyses.slice(0, 3).map((analysis) => (
              <button
                key={analysis.id}
                role="menuitem"
                tabIndex={-1}
                onClick={() => handleNavigation(`/analysis/${analysis.id}`)}
                className="w-full flex items-center px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 focus:bg-slate-100 dark:focus:bg-slate-700 focus:outline-none transition-colors"
              >
                <ChartBarIcon className="h-4 w-4 mr-3 text-slate-500 dark:text-slate-400" />
                <div className="flex-1 text-left">
                  <div className="font-medium truncate">{analysis.ticker}</div>
                  <div className="text-xs text-slate-500 dark:text-slate-400 truncate">
                    {analysis.title}
                  </div>
                </div>
                <div
                  className={`text-xs px-2 py-1 rounded-full ${
                    analysis.status === 'completed'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : analysis.status === 'running'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}
                >
                  {analysis.status === 'completed'
                    ? '完成'
                    : analysis.status === 'running'
                    ? '进行中'
                    : '失败'}
                </div>
              </button>
            ))}
          </div>
          {recentAnalyses.length > 3 && (
            <div className="px-4 py-2 border-t border-slate-200 dark:border-slate-700">
              <button
                role="menuitem"
                tabIndex={-1}
                onClick={() => handleNavigation('/analysis/history')}
                className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
              >
                查看全部 {recentAnalyses.length} 个分析 →
              </button>
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className="border-t border-slate-200 dark:border-slate-700 pt-2">
        <button
          role="menuitem"
          tabIndex={-1}
          onClick={() => handleNavigation('/create-task')}
          className="w-full flex items-center px-4 py-3 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:outline-none transition-colors"
        >
          <ChartBarIcon className="h-5 w-5 mr-3" />
          <span className="font-medium">创建新分析</span>
        </button>
      </div>

      {/* Hidden descriptions for screen readers */}
      <div className="sr-only">
        <div id="分析历史-desc">查看所有历史分析记录</div>
        <div id="分析对比-desc">对比多个分析结果</div>
      </div>
    </div>
  );
}
