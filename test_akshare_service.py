#!/usr/bin/env python3
"""
AKShare 服务状态检查脚本
"""

import requests
import json
import sys

def test_service_endpoints():
    """测试服务的各个端点"""
    base_url = "http://localhost:5000"
    
    endpoints = [
        {"path": "/", "method": "GET", "description": "根路径"},
        {"path": "/health", "method": "GET", "description": "健康检查"},
        {"path": "/docs", "method": "GET", "description": "API文档"},
    ]
    
    print(f"测试 AKShare 服务: {base_url}")
    print("=" * 50)
    
    for endpoint in endpoints:
        try:
            if endpoint["method"] == "GET":
                response = requests.get(f"{base_url}{endpoint['path']}", timeout=5)
            else:
                response = requests.post(f"{base_url}{endpoint['path']}", timeout=5)
            
            print(f"✅ {endpoint['description']} ({endpoint['path']}): {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应: {json.dumps(data, ensure_ascii=False)[:100]}...")
                except:
                    print(f"   响应: {response.text[:100]}...")
            
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint['description']} ({endpoint['path']}): 连接失败 - 服务可能未启动")
        except requests.exceptions.Timeout:
            print(f"⏰ {endpoint['description']} ({endpoint['path']}): 请求超时")
        except Exception as e:
            print(f"❌ {endpoint['description']} ({endpoint['path']}): {str(e)}")
        
        print()

def test_data_endpoints():
    """测试数据获取端点"""
    base_url = "http://localhost:5000"
    
    test_data = [
        {
            "path": "/api/stock/history",
            "method": "POST",
            "data": {
                "symbol": "000001",
                "period": "daily",
                "time_period": "1w",
                "workflow_id": "test_workflow_123"
            },
            "description": "股票历史数据"
        },
        {
            "path": "/api/stock/news",
            "method": "POST", 
            "data": {
                "symbol": "000001",
                "limit": 5,
                "workflow_id": "test_workflow_123"
            },
            "description": "股票新闻"
        }
    ]
    
    print("测试数据端点:")
    print("=" * 50)
    
    for test in test_data:
        try:
            response = requests.post(
                f"{base_url}{test['path']}", 
                json=test['data'],
                timeout=30
            )
            
            print(f"📊 {test['description']} ({test['path']}): {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    success = data.get('success', False)
                    count = data.get('count', 0)
                    source = data.get('source', 'unknown')
                    print(f"   成功: {success}, 数据量: {count}, 来源: {source}")
                except:
                    print(f"   响应: {response.text[:100]}...")
            else:
                print(f"   错误: {response.text}")
            
        except requests.exceptions.ConnectionError:
            print(f"❌ {test['description']}: 连接失败")
        except Exception as e:
            print(f"❌ {test['description']}: {str(e)}")
        
        print()

def main():
    """主函数"""
    print("AKShare 服务状态检查")
    print("=" * 50)
    
    # 1. 测试基础端点
    test_service_endpoints()
    
    # 2. 测试数据端点
    test_data_endpoints()
    
    print("检查完成!")

if __name__ == "__main__":
    main()