-- 添加风险评估表的数据库迁移脚本
-- 版本: 1.0
-- 创建时间: 2025-01-08
-- 说明: 为 TradingAgents 系统添加风险评估功能支持

USE trading_analysis;

-- ============================================================================
-- 添加风险评估表
-- ============================================================================

-- 风险评估主表
CREATE TABLE IF NOT EXISTS risk_assessments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    risk_id VARCHAR(36) UNIQUE NOT NULL COMMENT '风险评估唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    overall_risk_level ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium' COMMENT '整体风险等级',
    risk_score INT NOT NULL DEFAULT 5 COMMENT '风险评分 (1-10)',
    summary TEXT COMMENT '风险评估摘要',
    market_risk JSON COMMENT '市场风险详情',
    liquidity_risk JSON COMMENT '流动性风险详情',
    credit_risk JSON COMMENT '信用风险详情',
    operational_risk JSON COMMENT '操作风险详情',
    scenario_analysis JSON COMMENT '情景分析结果',
    risk_metrics JSON COMMENT '风险指标计算结果',
    recommendations JSON COMMENT '风险控制建议',
    risk_controls JSON COMMENT '风险控制参数',
    risk_warnings JSON COMMENT '风险预警信息',
    status ENUM('completed', 'failed') DEFAULT 'completed' COMMENT '评估状态',
    execution_time_ms INT COMMENT '执行耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_risk_id (risk_id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_risk_level (overall_risk_level),
    INDEX idx_risk_score (risk_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风险评估结果表';

-- ============================================================================
-- 更新工作流概览视图以包含风险评估信息
-- ============================================================================

-- 删除旧视图
DROP VIEW IF EXISTS workflow_overview;

-- 重新创建包含风险评估的工作流概览视图
CREATE OR REPLACE VIEW workflow_overview AS
SELECT
    w.workflow_id,
    w.ticker,
    w.title,
    w.status,
    w.current_stage,
    w.progress,
    w.created_at,
    w.completed_at,
    w.created_by,
    TIMESTAMPDIFF(SECOND, w.started_at, w.completed_at) as duration_seconds,
    (SELECT COUNT(*) FROM analyst_reports ar WHERE ar.workflow_id = w.workflow_id) as report_count,
    (SELECT COUNT(*) FROM workflow_events we WHERE we.workflow_id = w.workflow_id AND we.event_type = 'error') as error_count,
    ra.overall_risk_level,
    ra.risk_score,
    CASE WHEN ra.risk_id IS NOT NULL THEN TRUE ELSE FALSE END as risk_assessment_completed
FROM workflows w
LEFT JOIN risk_assessments ra ON w.workflow_id = ra.workflow_id;

-- ============================================================================
-- 更新完整报告视图以包含风险评估
-- ============================================================================

-- 删除旧视图
DROP VIEW IF EXISTS full_final_report;

-- 重新创建包含风险评估的完整报告视图
CREATE OR REPLACE VIEW full_final_report AS
SELECT
    w.workflow_id,
    w.ticker,
    w.title,
    w.status,
    fd.decision_type,
    fd.confidence_level,
    fd.decision_rationale,
    (SELECT GROUP_CONCAT(ar.summary SEPARATOR '\n---\n') FROM analyst_reports ar WHERE ar.workflow_id = w.workflow_id) as analyst_summaries,
    (SELECT GROUP_CONCAT(rr.summary SEPARATOR '\n---\n') FROM research_reports rr WHERE rr.workflow_id = w.workflow_id) as research_summaries,
    ds.summary as debate_summary,
    ra.overall_risk_level,
    ra.risk_score,
    ra.summary as risk_summary
FROM workflows w
LEFT JOIN final_decisions fd ON w.workflow_id = fd.workflow_id
LEFT JOIN debate_sessions ds ON w.workflow_id = ds.workflow_id
LEFT JOIN risk_assessments ra ON w.workflow_id = ra.workflow_id
WHERE w.status = 'completed';

-- ============================================================================
-- 添加风险评估相关的存储过程
-- ============================================================================

-- 保存风险评估结果
DROP PROCEDURE IF EXISTS SaveRiskAssessment;
DELIMITER //
CREATE PROCEDURE SaveRiskAssessment(
    IN p_risk_id VARCHAR(36),
    IN p_workflow_id VARCHAR(36),
    IN p_overall_risk_level ENUM('low', 'medium', 'high'),
    IN p_risk_score INT,
    IN p_summary TEXT,
    IN p_market_risk JSON,
    IN p_liquidity_risk JSON,
    IN p_credit_risk JSON,
    IN p_operational_risk JSON,
    IN p_scenario_analysis JSON,
    IN p_risk_metrics JSON,
    IN p_recommendations JSON,
    IN p_risk_controls JSON,
    IN p_risk_warnings JSON,
    IN p_status ENUM('completed', 'failed'),
    IN p_execution_time_ms INT
)
BEGIN
    INSERT INTO risk_assessments (
        risk_id, workflow_id, overall_risk_level, risk_score, summary,
        market_risk, liquidity_risk, credit_risk, operational_risk,
        scenario_analysis, risk_metrics, recommendations, risk_controls,
        risk_warnings, status, execution_time_ms
    ) VALUES (
        p_risk_id, p_workflow_id, p_overall_risk_level, p_risk_score, p_summary,
        p_market_risk, p_liquidity_risk, p_credit_risk, p_operational_risk,
        p_scenario_analysis, p_risk_metrics, p_recommendations, p_risk_controls,
        p_risk_warnings, p_status, p_execution_time_ms
    );
END //
DELIMITER ;

-- 获取工作流的风险评估结果
DROP PROCEDURE IF EXISTS GetRiskAssessment;
DELIMITER //
CREATE PROCEDURE GetRiskAssessment(
    IN p_workflow_id VARCHAR(36)
)
BEGIN
    SELECT * FROM risk_assessments 
    WHERE workflow_id = p_workflow_id 
    ORDER BY created_at DESC 
    LIMIT 1;
END //
DELIMITER ;

-- ============================================================================
-- 完成迁移
-- ============================================================================

SELECT 'Risk Assessment table migration completed successfully!' as status;