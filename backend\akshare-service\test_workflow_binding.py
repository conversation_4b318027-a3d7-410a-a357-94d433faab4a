#!/usr/bin/env python3
"""
AKShare 工作流绑定功能测试脚本
测试数据获取与工作流ID的绑定功能
"""

import requests
import json
import uuid
from datetime import datetime

# 配置
BASE_URL = "http://localhost:5000"
TEST_WORKFLOW_ID = f"test_workflow_{uuid.uuid4().hex[:8]}"
TEST_SYMBOL = "000001"  # 平安银行

def test_api_endpoint(endpoint, data, description):
    """测试API端点"""
    print(f"\n=== 测试 {description} ===")
    print(f"端点: {endpoint}")
    print(f"数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(f"{BASE_URL}{endpoint}", json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"成功: {result.get('success', False)}")
            print(f"数据量: {result.get('count', 0)}")
            print(f"来源: {result.get('source', 'unknown')}")
            return True
        else:
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False

def test_health_check():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"服务状态: {result.get('status', 'unknown')}")
            return True
        else:
            print(f"健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"健康检查请求失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print(f"开始测试 AKShare 工作流绑定功能")
    print(f"测试工作流ID: {TEST_WORKFLOW_ID}")
    print(f"测试股票代码: {TEST_SYMBOL}")
    print(f"服务地址: {BASE_URL}")
    
    # 1. 健康检查
    if not test_health_check():
        print("❌ 服务不可用，退出测试")
        return
    
    # 2. 测试股票历史数据
    success_count = 0
    total_tests = 4
    
    if test_api_endpoint("/api/stock/history", {
        "symbol": TEST_SYMBOL,
        "period": "daily",
        "time_period": "1m",
        "workflow_id": TEST_WORKFLOW_ID
    }, "股票历史数据"):
        success_count += 1
    
    # 3. 测试股票新闻
    if test_api_endpoint("/api/stock/news", {
        "symbol": TEST_SYMBOL,
        "limit": 10,
        "workflow_id": TEST_WORKFLOW_ID
    }, "股票新闻"):
        success_count += 1
    
    # 4. 测试基本面数据
    if test_api_endpoint("/api/stock/fundamental", {
        "symbol": TEST_SYMBOL,
        "indicator": "all",
        "period_type": "按报告期",
        "workflow_id": TEST_WORKFLOW_ID
    }, "基本面数据"):
        success_count += 1
    
    # 5. 测试实时数据
    if test_api_endpoint("/api/stock/realtime", {
        "symbol": TEST_SYMBOL,
        "workflow_id": TEST_WORKFLOW_ID
    }, "实时数据"):
        success_count += 1
    
    # 6. 测试技术指标
    if test_api_endpoint("/api/stock/technical", {
        "symbol": TEST_SYMBOL,
        "indicator": "RSI",
        "period": "14",
        "workflow_id": TEST_WORKFLOW_ID
    }, "技术指标"):
        success_count += 1
        total_tests += 1
    
    # 测试结果
    print(f"\n=== 测试结果 ===")
    print(f"成功: {success_count}/{total_tests}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！")
        print(f"📊 可以在数据库中查询工作流 {TEST_WORKFLOW_ID} 的数据获取记录")
        print("SQL 查询示例:")
        print(f"  SELECT * FROM workflow_events WHERE workflow_id = '{TEST_WORKFLOW_ID}';")
        print(f"  SELECT * FROM data_fetch_logs WHERE workflow_id = '{TEST_WORKFLOW_ID}';")
    else:
        print("❌ 部分测试失败，请检查服务配置")

if __name__ == "__main__":
    main()