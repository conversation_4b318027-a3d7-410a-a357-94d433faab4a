import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { dbConfig } from '@/lib/db-config';

/**
 * 获取缓存的新闻数据
 * GET /api/cache/news?symbol=000001&limit=10
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const symbol = searchParams.get('symbol');
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam) : 10;

    if (!symbol) {
      return NextResponse.json(
        { 
          success: false, 
          message: '缺少股票代码参数' 
        },
        { status: 400 }
      );
    }

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询缓存的新闻数据
      const query = `
        SELECT * FROM financial_news 
        WHERE JSON_CONTAINS(related_tickers, ?) OR first_related_ticker = ?
        ORDER BY publish_time DESC 
        LIMIT ?
      `;
      
      const symbolJson = JSON.stringify([symbol]);
      const params: any[] = [symbolJson, symbol, limit];

      const [rows] = await connection.execute(query, params);

      await connection.end();

      return NextResponse.json({
        success: true,
        data: rows,
        count: Array.isArray(rows) ? rows.length : 0
      });

    } catch (queryError) {
      await connection.end();
      throw queryError;
    }

  } catch (error) {
    console.error('[缓存新闻数据API] 错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

/**
 * 检查新闻数据缓存是否存在
 * GET /api/cache/news/exists?symbol=000001
 */
export async function HEAD(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const symbol = searchParams.get('symbol');

    if (!symbol) {
      return NextResponse.json(
        { 
          success: false, 
          message: '缺少股票代码参数' 
        },
        { status: 400 }
      );
    }

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询缓存的新闻数据
      const query = `
        SELECT COUNT(*) as count FROM financial_news 
        WHERE JSON_CONTAINS(related_tickers, ?) OR first_related_ticker = ?
      `;
      
      const symbolJson = JSON.stringify([symbol]);
      const params: any[] = [symbolJson, symbol];

      const [rows]: any = await connection.execute(query, params);
      const exists = rows[0].count > 0;

      await connection.end();

      return NextResponse.json({
        success: true,
        exists
      });

    } catch (queryError) {
      await connection.end();
      throw queryError;
    }

  } catch (error) {
    console.error('[检查新闻数据缓存API] 错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}