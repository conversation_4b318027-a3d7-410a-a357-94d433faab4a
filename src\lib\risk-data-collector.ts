/**
 * 风险数据收集与处理模块
 * 负责收集和处理风险分析所需的各类数据
 */

import { akshareAdapter } from './akshare/adapter';

// 价格数据接口
interface PriceData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// 风险指标计算器（简化版本）
class RiskMetricsCalculator {
  calculateRiskMetrics(priceData: PriceData[], marketData?: PriceData[]) {
    const returns = this.calculateReturns(priceData.map((d) => d.close));
    const volatility = this.calculateVolatility(returns);

    let beta = 1.0;
    let correlation = 0.5;

    if (marketData && marketData.length === priceData.length) {
      const marketReturns = this.calculateReturns(marketData.map((d) => d.close));
      correlation = this.calculateCorrelation(returns, marketReturns);
      const stockVol = this.standardDeviation(returns);
      const marketVol = this.standardDeviation(marketReturns);
      beta = correlation * (stockVol / marketVol);
    }

    const maxDrawdown = this.calculateMaxDrawdown(priceData.map((d) => d.close));
    const sharpeRatio = this.calculateSharpeRatio(returns);

    return {
      volatility: {
        daily_volatility: volatility,
        weekly_volatility: volatility * Math.sqrt(5),
        monthly_volatility: volatility * Math.sqrt(21),
        annualized_volatility: volatility * Math.sqrt(252),
      },
      ratios: {
        beta,
        sharpe_ratio: sharpeRatio,
        sortino_ratio: sharpeRatio * 1.2, // 简化计算
      },
      correlation: {
        market_correlation: correlation,
      },
      var: {
        var_95_1d: this.calculateVaR(returns, 0.95),
        var_99_1d: this.calculateVaR(returns, 0.99),
        expected_shortfall_95: this.calculateVaR(returns, 0.95) * 1.3, // 简化计算
      },
      drawdown: {
        max_drawdown: maxDrawdown,
      },
    };
  }

  private calculateReturns(prices: number[]): number[] {
    const returns: number[] = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }
    return returns;
  }

  private calculateVolatility(returns: number[]): number {
    return this.standardDeviation(returns);
  }

  private standardDeviation(values: number[]): number {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map((value) => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / squaredDiffs.length;
    return Math.sqrt(avgSquaredDiff);
  }

  private calculateCorrelation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    const sumX = x.slice(0, n).reduce((a, b) => a + b, 0);
    const sumY = y.slice(0, n).reduce((a, b) => a + b, 0);
    const sumXY = x.slice(0, n).reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.slice(0, n).reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.slice(0, n).reduce((sum, yi) => sum + yi * yi, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  private calculateMaxDrawdown(prices: number[]): number {
    let maxDrawdown = 0;
    let peak = prices[0];

    for (const price of prices) {
      if (price > peak) {
        peak = price;
      }
      const drawdown = (peak - price) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return maxDrawdown;
  }

  private calculateSharpeRatio(returns: number[]): number {
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const volatility = this.standardDeviation(returns);
    const riskFreeRate = 0.03 / 252; // 假设年化无风险利率3%
    return volatility === 0 ? 0 : (avgReturn - riskFreeRate) / volatility;
  }

  private calculateVaR(returns: number[], confidence: number): number {
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const index = Math.floor((1 - confidence) * sortedReturns.length);
    return Math.abs(sortedReturns[index] || 0);
  }
}

const riskMetricsCalculator = new RiskMetricsCalculator();

export interface RiskDataCollection {
  ticker: string;
  timestamp: string;
  market_data: MarketRiskData;
  financial_data: FinancialRiskData;
  liquidity_data: LiquidityRiskData;
  macro_data: MacroRiskData;
  data_quality: DataQualityMetrics;
}

export interface MarketRiskData {
  current_price: number;
  historical_prices: number[];
  volatility_data: {
    daily_volatility: number;
    weekly_volatility: number;
    monthly_volatility: number;
    annualized_volatility: number;
  };
  beta_data: {
    beta_coefficient: number;
    correlation_with_market: number;
    r_squared: number;
  };
  price_statistics: {
    max_price_52w: number;
    min_price_52w: number;
    average_price_30d: number;
    price_change_1d: number;
    price_change_7d: number;
    price_change_30d: number;
  };
}

export interface FinancialRiskData {
  debt_metrics: {
    total_debt: number;
    debt_to_equity: number;
    debt_to_assets: number;
    interest_coverage_ratio: number;
    current_ratio: number;
    quick_ratio: number;
  };
  profitability_metrics: {
    roe: number;
    roa: number;
    gross_margin: number;
    operating_margin: number;
    net_margin: number;
  };
  cash_flow_metrics: {
    operating_cash_flow: number;
    free_cash_flow: number;
    cash_flow_to_debt: number;
    cash_and_equivalents: number;
  };
  financial_health_score: number;
}

export interface LiquidityRiskData {
  volume_data: {
    average_daily_volume: number;
    volume_trend_30d: number;
    volume_volatility: number;
    turnover_rate: number;
  };
  spread_data: {
    bid_ask_spread: number;
    spread_percentage: number;
    market_depth: number;
  };
  liquidity_metrics: {
    amihud_ratio: number;
    trading_frequency: number;
    price_impact: number;
  };
}

export interface MacroRiskData {
  market_indices: {
    market_correlation: number;
    sector_correlation: number;
    market_beta: number;
  };
  economic_indicators: {
    interest_rate_sensitivity: number;
    inflation_sensitivity: number;
    gdp_correlation: number;
  };
}

export interface DataQualityMetrics {
  completeness_score: number;
  timeliness_score: number;
  accuracy_score: number;
  consistency_score: number;
  overall_quality_score: number;
  missing_data_fields: string[];
  data_age_hours: number;
}

/**
 * 风险数据收集器类
 */
export class RiskDataCollector {
  private static instance: RiskDataCollector;
  private currentWorkflowId: string | null = null;

  public static getInstance(): RiskDataCollector {
    if (!RiskDataCollector.instance) {
      RiskDataCollector.instance = new RiskDataCollector();
    }
    return RiskDataCollector.instance;
  }

  /**
   * 设置当前工作流ID
   */
  public setWorkflowId(workflowId: string | null): void {
    this.currentWorkflowId = workflowId;
    console.log(`[RiskDataCollector] 设置工作流ID: ${workflowId}`);
  }

  /**
   * 获取当前工作流ID
   */
  public getWorkflowId(): string | null {
    return this.currentWorkflowId;
  }

  /**
   * 在指定工作流上下文中收集风险数据
   * 执行完成后会恢复之前的工作流ID
   */
  public async collectRiskDataWithWorkflow<T>(
    workflowId: string,
    ticker: string
  ): Promise<RiskDataCollection> {
    const previousWorkflowId = this.currentWorkflowId;
    this.setWorkflowId(workflowId);

    try {
      return await this.collectRiskData(ticker);
    } finally {
      this.setWorkflowId(previousWorkflowId);
    }
  }

  /**
   * 收集指定股票的风险数据
   */
  public async collectRiskData(ticker: string, workflowId?: string): Promise<RiskDataCollection> {
    console.log(`[RiskDataCollector] Starting data collection for ${ticker}`);

    // 如果传入了工作流ID，则设置为当前工作流ID
    const effectiveWorkflowId = workflowId || this.currentWorkflowId;
    if (effectiveWorkflowId) {
      console.log(`[RiskDataCollector] 使用工作流ID: ${effectiveWorkflowId}`);
    }

    try {
      const startTime = Date.now();

      // 并行收集各类数据，传递工作流ID
      const [marketData, financialData, liquidityData, macroData] = await Promise.allSettled([
        this.collectMarketRiskData(ticker, effectiveWorkflowId),
        this.collectFinancialRiskData(ticker, effectiveWorkflowId),
        this.collectLiquidityRiskData(ticker, effectiveWorkflowId),
        this.collectMacroRiskData(ticker, effectiveWorkflowId),
      ]);

      // 处理数据收集结果
      const riskData: RiskDataCollection = {
        ticker,
        timestamp: new Date().toISOString(),
        market_data:
          marketData.status === 'fulfilled' ? marketData.value : this.getDefaultMarketData(),
        financial_data:
          financialData.status === 'fulfilled'
            ? financialData.value
            : this.getDefaultFinancialData(),
        liquidity_data:
          liquidityData.status === 'fulfilled'
            ? liquidityData.value
            : this.getDefaultLiquidityData(),
        macro_data: macroData.status === 'fulfilled' ? macroData.value : this.getDefaultMacroData(),
        data_quality: this.assessDataQuality([marketData, financialData, liquidityData, macroData]),
      };

      const collectionTime = Date.now() - startTime;
      console.log(`[RiskDataCollector] Data collection completed in ${collectionTime}ms`);

      return riskData;
    } catch (error) {
      console.error(`[RiskDataCollector] Error collecting risk data for ${ticker}:`, error);
      throw new Error(`风险数据收集失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 收集市场风险数据
   */
  private async collectMarketRiskData(ticker: string, workflowId?: string | null): Promise<MarketRiskData> {
    try {
      // 获取历史价格数据
      const historicalData = await akshareAdapter.getHistoricalData(ticker, 252, workflowId || undefined); // 一年的交易日
      const currentPrice = await akshareAdapter.getCurrentPrice(ticker, workflowId || undefined);

      if (!historicalData || historicalData.length === 0) {
        throw new Error('无法获取历史价格数据');
      }

      // 转换数据格式以适配风险指标计算器
      // Python 后端返回的字段名可能是中文或英文，需要兼容处理
      const priceData: PriceData[] = historicalData.map((d) => ({
        date: d.trade_date || d.date || d['日期'] || '',
        open: d.open || d['开盘'] || 0,
        high: d.high || d['最高'] || 0,
        low: d.low || d['最低'] || 0,
        close: d.close || d['收盘'] || 0,
        volume: d.volume || d['成交量'] || 0,
      }));

      // 获取市场指数数据用于Beta计算
      let marketData: PriceData[] | undefined;
      try {
        const marketHistoricalData = await akshareAdapter.getMarketIndexData(
          '000300',
          historicalData.length,
          workflowId || undefined
        );
        if (marketHistoricalData && marketHistoricalData.length > 0) {
          marketData = marketHistoricalData.map((d) => ({
            date: d.trade_date || d.date || d['日期'] || '',
            open: d.open || d['开盘'] || 0,
            high: d.high || d['最高'] || 0,
            low: d.low || d['最低'] || 0,
            close: d.close || d['收盘'] || 0,
            volume: d.volume || d['成交量'] || 0,
          }));
        }
      } catch (marketError) {
        console.warn(
          '[RiskDataCollector] Failed to get market data for Beta calculation:',
          marketError
        );
      }

      // 使用风险指标计算引擎计算各项指标
      const riskMetrics = riskMetricsCalculator.calculateRiskMetrics(priceData, marketData);

      // 计算价格统计数据
      const priceStatistics = this.calculatePriceStatistics(historicalData, currentPrice);

      return {
        current_price: currentPrice,
        historical_prices: historicalData.map((d) => d.close || d['收盘'] || 0),
        volatility_data: {
          daily_volatility: riskMetrics.volatility.daily_volatility,
          weekly_volatility: riskMetrics.volatility.weekly_volatility,
          monthly_volatility: riskMetrics.volatility.monthly_volatility,
          annualized_volatility: riskMetrics.volatility.annualized_volatility,
        },
        beta_data: {
          beta_coefficient: riskMetrics.ratios.beta,
          correlation_with_market: riskMetrics.correlation.market_correlation,
          r_squared: Math.pow(riskMetrics.correlation.market_correlation, 2),
        },
        price_statistics: priceStatistics,
      };
    } catch (error) {
      console.error(`[RiskDataCollector] Error collecting market risk data:`, error);
      throw error;
    }
  }

  /**
   * 收集财务风险数据
   */
  private async collectFinancialRiskData(ticker: string, workflowId?: string | null): Promise<FinancialRiskData> {
    try {
      // 获取财务数据
      const financialStatements = await akshareAdapter.getFinancialData({
        symbol: ticker,
        indicator: 'all',
        period_type: '按单季度',
        time_period: '1y',
        workflow_id: workflowId || undefined,
      });

      if (!financialStatements) {
        throw new Error('无法获取财务数据');
      }

      // 计算债务指标
      const debtMetrics = this.calculateDebtMetrics(financialStatements);

      // 计算盈利能力指标
      const profitabilityMetrics = this.calculateProfitabilityMetrics(financialStatements);

      // 计算现金流指标
      const cashFlowMetrics = this.calculateCashFlowMetrics(financialStatements);

      // 计算财务健康度评分
      const financialHealthScore = this.calculateFinancialHealthScore(
        debtMetrics,
        profitabilityMetrics,
        cashFlowMetrics
      );

      return {
        debt_metrics: debtMetrics,
        profitability_metrics: profitabilityMetrics,
        cash_flow_metrics: cashFlowMetrics,
        financial_health_score: financialHealthScore,
      };
    } catch (error) {
      console.error(`[RiskDataCollector] Error collecting financial risk data:`, error);
      throw error;
    }
  }

  /**
   * 收集流动性风险数据
   */
  private async collectLiquidityRiskData(ticker: string, workflowId?: string | null): Promise<LiquidityRiskData> {
    try {
      // 获取成交量数据
      const volumeData = await akshareAdapter.getVolumeData(ticker, 30, workflowId || undefined);

      // 获取买卖价差数据
      const spreadData = await akshareAdapter.getSpreadData(ticker, workflowId || undefined);

      if (!volumeData || volumeData.length === 0) {
        throw new Error('无法获取成交量数据');
      }

      // 计算成交量指标
      const volumeMetrics = this.calculateVolumeMetrics(volumeData);

      // 计算价差指标
      const spreadMetrics = this.calculateSpreadMetrics(spreadData);

      // 计算流动性指标
      const liquidityMetrics = this.calculateLiquidityMetrics(volumeData, spreadData);

      return {
        volume_data: volumeMetrics,
        spread_data: spreadMetrics,
        liquidity_metrics: liquidityMetrics,
      };
    } catch (error) {
      console.error(`[RiskDataCollector] Error collecting liquidity risk data:`, error);
      throw error;
    }
  }

  /**
   * 收集宏观风险数据
   */
  private async collectMacroRiskData(ticker: string, workflowId?: string | null): Promise<MacroRiskData> {
    try {
      // 获取市场指数数据
      const marketIndices = await this.getMarketCorrelations(ticker, workflowId);

      // 获取经济指标敏感度
      const economicIndicators = await this.getEconomicSensitivity(ticker, workflowId);

      return {
        market_indices: marketIndices,
        economic_indicators: economicIndicators,
      };
    } catch (error) {
      console.error(`[RiskDataCollector] Error collecting macro risk data:`, error);
      throw error;
    }
  }

  /**
   * 计算价格统计数据
   */
  private calculatePriceStatistics(historicalData: any[], currentPrice: number): any {
    const prices = historicalData.map((d) => d.close);
    const recent30d = prices.slice(-30);
    const recent7d = prices.slice(-7);

    return {
      max_price_52w: Math.max(...prices),
      min_price_52w: Math.min(...prices),
      average_price_30d: recent30d.reduce((a, b) => a + b, 0) / recent30d.length,
      price_change_1d:
        prices.length > 1
          ? ((currentPrice - prices[prices.length - 1]) / prices[prices.length - 1]) * 100
          : 0,
      price_change_7d: recent7d.length > 0 ? ((currentPrice - recent7d[0]) / recent7d[0]) * 100 : 0,
      price_change_30d:
        recent30d.length > 0 ? ((currentPrice - recent30d[0]) / recent30d[0]) * 100 : 0,
    };
  }

  // 其他计算方法的占位符实现
  private calculateDebtMetrics(financialData: any): any {
    return {
      total_debt: financialData.total_debt || 0,
      debt_to_equity: financialData.debt_to_equity || 0,
      debt_to_assets: financialData.debt_to_assets || 0,
      interest_coverage_ratio: financialData.interest_coverage_ratio || 0,
      current_ratio: financialData.current_ratio || 1,
      quick_ratio: financialData.quick_ratio || 1,
    };
  }

  private calculateProfitabilityMetrics(financialData: any): any {
    return {
      roe: financialData.roe || 0,
      roa: financialData.roa || 0,
      gross_margin: financialData.gross_margin || 0,
      operating_margin: financialData.operating_margin || 0,
      net_margin: financialData.net_margin || 0,
    };
  }

  private calculateCashFlowMetrics(financialData: any): any {
    return {
      operating_cash_flow: financialData.operating_cash_flow || 0,
      free_cash_flow: financialData.free_cash_flow || 0,
      cash_flow_to_debt: financialData.cash_flow_to_debt || 0,
      cash_and_equivalents: financialData.cash_and_equivalents || 0,
    };
  }

  private calculateFinancialHealthScore(debt: any, profitability: any, cashFlow: any): number {
    // 简化的财务健康度评分算法
    let score = 50; // 基础分

    // 债务指标影响
    if (debt.debt_to_equity < 0.5) score += 10;
    else if (debt.debt_to_equity > 1.5) score -= 10;

    // 盈利能力影响
    if (profitability.roe > 0.15) score += 10;
    else if (profitability.roe < 0) score -= 15;

    // 现金流影响
    if (cashFlow.free_cash_flow > 0) score += 10;
    else score -= 10;

    return Math.max(0, Math.min(100, score));
  }

  private calculateVolumeMetrics(volumeData: any[]): any {
    const volumes = volumeData.map((d) => d.volume);
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;

    return {
      average_daily_volume: avgVolume,
      volume_trend_30d: this.calculateTrend(volumes),
      volume_volatility: this.calculateStandardDeviation(volumes) / avgVolume,
      turnover_rate: 0.05, // 占位符
    };
  }

  private calculateSpreadMetrics(spreadData: any): any {
    return {
      bid_ask_spread: spreadData?.spread || 0.01,
      spread_percentage: spreadData?.spread_percentage || 0.1,
      market_depth: spreadData?.market_depth || 1000000,
    };
  }

  private calculateLiquidityMetrics(volumeData: any[], spreadData: any): any {
    return {
      amihud_ratio: 0.001, // 占位符
      trading_frequency: volumeData.length / 30,
      price_impact: 0.01, // 占位符
    };
  }

  private calculateTrend(values: number[]): number {
    // 简单的线性趋势计算
    const n = values.length;
    const sumX = (n * (n + 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, i) => sum + (i + 1) * y, 0);
    const sumXX = (n * (n + 1) * (2 * n + 1)) / 6;

    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  private calculateStandardDeviation(values: number[]): number {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map((value) => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / squaredDiffs.length;
    return Math.sqrt(avgSquaredDiff);
  }

  private async getMarketCorrelations(ticker: string, workflowId?: string | null): Promise<any> {
    // 这里可以添加实际的市场相关性计算逻辑
    // 目前返回模拟数据
    console.log(`[RiskDataCollector] 计算市场相关性 - 股票: ${ticker}, 工作流: ${workflowId || 'none'}`);
    return {
      market_correlation: 0.6,
      sector_correlation: 0.8,
      market_beta: 1.0,
    };
  }

  private async getEconomicSensitivity(ticker: string, workflowId?: string | null): Promise<any> {
    // 这里可以添加实际的经济敏感度计算逻辑
    // 目前返回模拟数据
    console.log(`[RiskDataCollector] 计算经济敏感度 - 股票: ${ticker}, 工作流: ${workflowId || 'none'}`);
    return {
      interest_rate_sensitivity: 0.5,
      inflation_sensitivity: 0.3,
      gdp_correlation: 0.4,
    };
  }

  /**
   * 评估数据质量
   */
  private assessDataQuality(dataResults: PromiseSettledResult<any>[]): DataQualityMetrics {
    const successCount = dataResults.filter((r) => r.status === 'fulfilled').length;
    const totalCount = dataResults.length;

    const completenessScore = (successCount / totalCount) * 100;
    const timelinessScore = 90; // 假设数据较新
    const accuracyScore = 85; // 假设数据准确性
    const consistencyScore = 88; // 假设数据一致性

    const overallScore =
      (completenessScore + timelinessScore + accuracyScore + consistencyScore) / 4;

    const missingFields = dataResults
      .map((result, index) =>
        result.status === 'rejected'
          ? ['market_data', 'financial_data', 'liquidity_data', 'macro_data'][index]
          : null
      )
      .filter((field) => field !== null) as string[];

    return {
      completeness_score: completenessScore,
      timeliness_score: timelinessScore,
      accuracy_score: accuracyScore,
      consistency_score: consistencyScore,
      overall_quality_score: overallScore,
      missing_data_fields: missingFields,
      data_age_hours: 1, // 假设数据1小时内
    };
  }

  // 默认数据方法
  private getDefaultMarketData(): MarketRiskData {
    return {
      current_price: 0,
      historical_prices: [],
      volatility_data: {
        daily_volatility: 0.02,
        weekly_volatility: 0.045,
        monthly_volatility: 0.092,
        annualized_volatility: 0.32,
      },
      beta_data: {
        beta_coefficient: 1.0,
        correlation_with_market: 0.5,
        r_squared: 0.25,
      },
      price_statistics: {
        max_price_52w: 0,
        min_price_52w: 0,
        average_price_30d: 0,
        price_change_1d: 0,
        price_change_7d: 0,
        price_change_30d: 0,
      },
    };
  }

  private getDefaultFinancialData(): FinancialRiskData {
    return {
      debt_metrics: {
        total_debt: 0,
        debt_to_equity: 0,
        debt_to_assets: 0,
        interest_coverage_ratio: 0,
        current_ratio: 1,
        quick_ratio: 1,
      },
      profitability_metrics: {
        roe: 0,
        roa: 0,
        gross_margin: 0,
        operating_margin: 0,
        net_margin: 0,
      },
      cash_flow_metrics: {
        operating_cash_flow: 0,
        free_cash_flow: 0,
        cash_flow_to_debt: 0,
        cash_and_equivalents: 0,
      },
      financial_health_score: 50,
    };
  }

  private getDefaultLiquidityData(): LiquidityRiskData {
    return {
      volume_data: {
        average_daily_volume: 0,
        volume_trend_30d: 0,
        volume_volatility: 0,
        turnover_rate: 0,
      },
      spread_data: {
        bid_ask_spread: 0.01,
        spread_percentage: 0.1,
        market_depth: 1000000,
      },
      liquidity_metrics: {
        amihud_ratio: 0.001,
        trading_frequency: 1,
        price_impact: 0.01,
      },
    };
  }

  private getDefaultMacroData(): MacroRiskData {
    return {
      market_indices: {
        market_correlation: 0.5,
        sector_correlation: 0.6,
        market_beta: 1.0,
      },
      economic_indicators: {
        interest_rate_sensitivity: 0.5,
        inflation_sensitivity: 0.3,
        gdp_correlation: 0.4,
      },
    };
  }
}

// 导出单例实例
export const riskDataCollector = RiskDataCollector.getInstance();
