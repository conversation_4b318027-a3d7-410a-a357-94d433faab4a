import { analysisService } from '@/lib/analysis-service';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ workflowId: string }> }
) {
    try {
        const { workflowId } = await params;
        const { searchParams } = new URL(request.url);
        const format = searchParams.get('format') || 'json';

        if (!workflowId) {
            return NextResponse.json(
                {
                    success: false,
                    error: '工作流ID不能为空',
                    timestamp: new Date().toISOString(),
                },
                { status: 400 }
            );
        }

        if (!['pdf', 'markdown', 'json'].includes(format)) {
            return NextResponse.json(
                {
                    success: false,
                    error: '不支持的导出格式',
                    timestamp: new Date().toISOString(),
                },
                { status: 400 }
            );
        }

        // Get analysis status
        const analysisStatus = await analysisService.getAnalysisStatus(workflowId);

        // Generate export data
        const exportData = {
            workflowId: analysisStatus.workflowId,
            ticker: analysisStatus.ticker,
            title: analysisStatus.title,
            status: analysisStatus.status,
            progress: analysisStatus.progress,
            createdAt: analysisStatus.createdAt,
            completedAt: analysisStatus.completedAt,
            analystReports: analysisStatus.analystReports,
            researchReports: analysisStatus.researchReports,
            finalDecision: analysisStatus.finalDecision,
            exportedAt: new Date().toISOString(),
        };

        let content: string;
        let contentType: string;
        let filename: string;

        switch (format) {
            case 'json':
                content = JSON.stringify(exportData, null, 2);
                contentType = 'application/json';
                filename = `${analysisStatus.ticker}_analysis_report.json`;
                break;

            case 'markdown':
                content = generateMarkdownReport(exportData);
                contentType = 'text/markdown';
                filename = `${analysisStatus.ticker}_analysis_report.md`;
                break;

            case 'pdf':
                // For now, return a simple text version
                // In a real implementation, you would use a PDF generation library
                content = generateTextReport(exportData);
                contentType = 'text/plain';
                filename = `${analysisStatus.ticker}_analysis_report.txt`;
                break;

            default:
                throw new Error('不支持的格式');
        }

        return new NextResponse(content, {
            headers: {
                'Content-Type': contentType,
                'Content-Disposition': `attachment; filename="${filename}"`,
            },
        });
    } catch (error: any) {
        console.error('Export report error:', error);

        // Handle specific error types
        if (error.code === 'ANALYSIS_NOT_FOUND') {
            return NextResponse.json(
                {
                    success: false,
                    error: '分析不存在',
                    timestamp: new Date().toISOString(),
                },
                { status: 404 }
            );
        }

        return NextResponse.json(
            {
                success: false,
                error: error.message || '导出报告失败',
                timestamp: new Date().toISOString(),
            },
            { status: 500 }
        );
    }
}

function generateMarkdownReport(data: any): string {
    return `# ${data.title} - 分析报告

## 基本信息
- **股票代码**: ${data.ticker}
- **工作流ID**: ${data.workflowId}
- **状态**: ${data.status}
- **进度**: ${data.progress}%
- **创建时间**: ${data.createdAt}
- **完成时间**: ${data.completedAt || '未完成'}

## 分析师报告
${data.analystReports.map((report: any) => `
### ${report.analyst_type} 分析师
- **状态**: ${report.status}
- **创建时间**: ${report.created_at}
- **摘要**: ${report.summary || '无摘要'}
`).join('\n')}

## 研究员报告
${data.researchReports.map((report: any) => `
### ${report.researcher_type} 研究员
- **状态**: ${report.status}
- **信心水平**: ${report.confidence_level || 'N/A'}
- **目标价格**: ${report.target_price || 'N/A'}
- **时间范围**: ${report.time_horizon || 'N/A'}
- **创建时间**: ${report.created_at}
- **摘要**: ${report.summary || '无摘要'}
`).join('\n')}

## 最终决策
${data.finalDecision ? `
- **决策类型**: ${data.finalDecision.decision_type || 'N/A'}
- **信心水平**: ${data.finalDecision.confidence_level || 'N/A'}
- **目标价格**: ${data.finalDecision.target_price || 'N/A'}
- **止损价格**: ${data.finalDecision.stop_loss || 'N/A'}
- **推理**: ${data.finalDecision.reasoning || '无推理'}
` : '暂无最终决策'}

---
*报告导出时间: ${data.exportedAt}*
`;
}

function generateTextReport(data: any): string {
    return `${data.title} - 分析报告

基本信息:
股票代码: ${data.ticker}
工作流ID: ${data.workflowId}
状态: ${data.status}
进度: ${data.progress}%
创建时间: ${data.createdAt}
完成时间: ${data.completedAt || '未完成'}

分析师报告:
${data.analystReports.map((report: any) => `
${report.analyst_type} 分析师:
  状态: ${report.status}
  创建时间: ${report.created_at}
  摘要: ${report.summary || '无摘要'}
`).join('\n')}

研究员报告:
${data.researchReports.map((report: any) => `
${report.researcher_type} 研究员:
  状态: ${report.status}
  信心水平: ${report.confidence_level || 'N/A'}
  目标价格: ${report.target_price || 'N/A'}
  时间范围: ${report.time_horizon || 'N/A'}
  创建时间: ${report.created_at}
  摘要: ${report.summary || '无摘要'}
`).join('\n')}

最终决策:
${data.finalDecision ? `
决策类型: ${data.finalDecision.decision_type || 'N/A'}
信心水平: ${data.finalDecision.confidence_level || 'N/A'}
目标价格: ${data.finalDecision.target_price || 'N/A'}
止损价格: ${data.finalDecision.stop_loss || 'N/A'}
推理: ${data.finalDecision.reasoning || '无推理'}
` : '暂无最终决策'}

报告导出时间: ${data.exportedAt}
`;
}