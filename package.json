{"name": "trading-agents-frontend", "version": "1.0.0", "description": "TradingAgents 多智能体大语言模型金融交易框架 - 前端界面", "private": true, "scripts": {"dev": "node scripts/check-env.js && next dev", "dev:no-check": "next dev", "build": "node scripts/check-env.js && next build", "build:no-check": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "db:test": "node test-db-connection.js", "db:setup": "node setup-database.js", "env:check": "node scripts/check-env.js"}, "dependencies": {"@ant-design/icons": "^6.0.2", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@langchain/community": "^0.3.6", "@langchain/core": "^0.3.6", "@langchain/langgraph": "^0.3.6", "@langchain/mcp-adapters": "^0.5.3", "@langchain/openai": "^0.3.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/uuid": "^10.0.0", "antd": "^5.27.4", "autoprefixer": "^10.4.16", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^12.23.16", "gsap": "^3.13.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.292.0", "mysql2": "^3.14.1", "next": "^15.3.5", "next-intl": "^4.3.5", "nodemailer": "^7.0.6", "postcss": "^8.4.31", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "recharts": "^2.8.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "socket.io-client": "^4.7.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "uuid": "^11.1.0", "web-vitals": "^5.1.0", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "@types/nodemailer": "^7.0.1", "eslint": "^8.0.0", "eslint-config-next": "^15.3.5", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "jest-environment-node": "^29.7.0", "webpack-bundle-analyzer": "^4.10.2"}, "engines": {"node": ">=18.0.0"}}