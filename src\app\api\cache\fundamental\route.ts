import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { dbConfig } from '@/lib/db-config';

/**
 * 获取缓存的基本面数据
 * GET /api/cache/fundamental?symbol=000001&indicator=all&period_type=按报告期
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const symbol = searchParams.get('symbol');
    const indicator = searchParams.get('indicator') || 'all';
    const period_type = searchParams.get('period_type') || '按报告期';
    const time_period = searchParams.get('time_period') || '';
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam) : 10;

    if (!symbol) {
      return NextResponse.json(
        { 
          success: false, 
          message: '缺少股票代码参数' 
        },
        { status: 400 }
      );
    }

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询缓存的基本面数据
      let query = `
        SELECT * FROM stock_fundamental_data 
        WHERE symbol = ? AND indicator = ? AND period_type = ?
      `;
      
      const params: any[] = [symbol, indicator, period_type];
      
      if (time_period) {
        query += ' AND time_period = ?';
        params.push(time_period);
      }
      
      query += ' ORDER BY created_at DESC LIMIT ?';
      params.push(limit);

      const [rows] = await connection.execute(query, params);

      await connection.end();

      return NextResponse.json({
        success: true,
        data: rows,
        count: Array.isArray(rows) ? rows.length : 0
      });

    } catch (queryError) {
      await connection.end();
      throw queryError;
    }

  } catch (error) {
    console.error('[缓存基本面数据API] 错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

/**
 * 检查基本面数据缓存是否存在
 * GET /api/cache/fundamental/exists?symbol=000001&indicator=all&period_type=按报告期
 */
export async function HEAD(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const symbol = searchParams.get('symbol');
    const indicator = searchParams.get('indicator') || 'all';
    const period_type = searchParams.get('period_type') || '按报告期';
    const time_period = searchParams.get('time_period') || '';

    if (!symbol) {
      return NextResponse.json(
        { 
          success: false, 
          message: '缺少股票代码参数' 
        },
        { status: 400 }
      );
    }

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询缓存的基本面数据
      let query = `
        SELECT COUNT(*) as count FROM stock_fundamental_data 
        WHERE symbol = ? AND indicator = ? AND period_type = ?
      `;
      
      const params: any[] = [symbol, indicator, period_type];
      
      if (time_period) {
        query += ' AND time_period = ?';
        params.push(time_period);
      }

      const [rows]: any = await connection.execute(query, params);
      const exists = rows[0].count > 0;

      await connection.end();

      return NextResponse.json({
        success: true,
        exists
      });

    } catch (queryError) {
      await connection.end();
      throw queryError;
    }

  } catch (error) {
    console.error('[检查基本面数据缓存API] 错误:', error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}