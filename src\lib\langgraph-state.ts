import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { Annotation, MemorySaver, messagesStateReducer, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { bearResearcherNode } from '../app/api/langgraph/analysis/agents/bear_researcher';
import { bullResearcherNode } from '../app/api/langgraph/analysis/agents/bull_researcher';
import { consensusEvaluatorNode } from '../app/api/langgraph/analysis/agents/consensus_evaluator';
import { dataCollectionNode } from '../app/api/langgraph/analysis/agents/data_collection';
import { debateModeratorNode } from '../app/api/langgraph/analysis/agents/debate_moderator';
import { fundamentalAnalystNode } from '../app/api/langgraph/analysis/agents/fundamental_analyst';
import { newsAnalystNode } from '../app/api/langgraph/analysis/agents/news_analyst';
import { riskManagerNode } from '../app/api/langgraph/analysis/agents/risk_manager';
import { sentimentAnalystNode } from '../app/api/langgraph/analysis/agents/sentiment_analyst';
import { technicalAnalystNode } from '../app/api/langgraph/analysis/agents/technical_analyst';
import { toolNode, tools } from './langgraph-tools';

// 根据 design-langgraph.md 的详细状态定义
export const TradingAgentAnnotation = Annotation.Root({
  ticker: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => '',
  }),
  date: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => new Date().toISOString().split('T')[0],
  }),
  config: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({
      deepThinkLLM: 'gpt-4o',
      quickThinkLLM: 'gpt-4o-mini',
      maxDebateRounds: 3,
      researchDepth: 'standard',
      onlineTools: true,
    }),
  }),
  data: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({}),
  }),
  analysis: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({}),
  }),
  research: Annotation({
    reducer: (x: any, y?: any) => (y ? { ...x, ...y } : x),
    default: () => ({ debateRounds: [] }),
  }),
  risk: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
  decision: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => null,
  }),
  currentStage: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => 'start',
  }),
  messages: Annotation({
    reducer: messagesStateReducer,
    default: () => [],
  }),
  status: Annotation({
    reducer: (x: any, y?: any) => y ?? x,
    default: () => 'pending',
  }),
  progress: Annotation({
    reducer: (x: number, y?: number) => y ?? x,
    default: () => 0,
  }),
  workflowId: Annotation({
    reducer: (x: string, y?: string) => y ?? x,
    default: () => `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  }),
});

// 判断是否继续执行的函数
export function shouldContinue(state: typeof TradingAgentAnnotation.State) {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  if (lastMessage.tool_calls?.length) {
    return 'tools';
  }
  return '__end__';
}

// 新增：判断辩论是否继续的路由函数
function routeDebate(state: typeof TradingAgentAnnotation.State) {
  if (state.research?.debateCompleted) {
    console.log('[Workflow] 辩论完成，进入共识评估');
    return 'consensus_evaluator';
  } else {
    console.log('[Workflow] 需要更多辩论轮次，返回研究团队协调员');
    return 'research_coordinator';
  }
}

// 调用模型的函数
export async function callModel(state: typeof TradingAgentAnnotation.State) {
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
  const OPENAI_BASE_URL =
    process.env.OPENAI_BASE_URL ||
    process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
    'https://api.nuwaapi.com/v1';
  const model = new ChatOpenAI({
    modelName: 'gpt-4o-mini',
    temperature: 0,
    apiKey: OPENAI_API_KEY,
    configuration: {
      baseURL: OPENAI_BASE_URL,
    },
  }).bindTools(tools);

  // 构建系统提示
  const systemPrompt = `你是一个专业的金融交易分析师。你的任务是：\n1. 分析股票的基本面、技术面和新闻情绪\n2. 评估投资风险\n3. 提供明确的交易建议\n\n当前分析的股票代码是: ${state.ticker}\n\n请使用可用的工具来收集和分析数据，然后提供专业的交易建议。`;

  const messages = [new HumanMessage(systemPrompt), ...state.messages];

  const response = await model.invoke(messages);
  return { messages: [response] };
}

// 新的综合节点
export async function summarizeAnalysisNode(state: typeof TradingAgentAnnotation.State) {
  const { fundamental, technical, sentiment, news } = state.analysis;

  let summary = '分析摘要:\n';
  if (fundamental) summary += `\n[基本面分析]\n${fundamental.summary}\n`;
  if (technical) summary += `\n[技术分析]\n${technical.summary}\n`;
  if (sentiment) summary += `\n[情绪分析]\n${sentiment.summary}\n`;
  if (news) summary += `\n[新闻摘要]\n${news.summary}\n`;

  return { messages: [new HumanMessage(summary)] };
}

// 研究团队协调节点
export async function researchTeamCoordinatorNode(state: typeof TradingAgentAnnotation.State) {
  console.log('[Research Team Coordinator] Coordinating research team...');

  // 检查分析师团队是否完成
  const { fundamental, technical, sentiment, news } = state.analysis;

  const coordinatorMessage =
    '研究团队协调员：分析师团队已完成初步分析，现在启动多头和空头研究员进行深度研究。';
  const messages = [...state.messages, new AIMessage(coordinatorMessage)];

  return {
    messages,
    currentStage: 'research_team_started',
    progress: Math.min(state.progress + 5, 100),
  };
}

// 风险评估节点 - 现在使用真实的风险管理师智能体
export async function riskAssessmentNode(state: typeof TradingAgentAnnotation.State) {
  return await riskManagerNode(state);
}

// 创建交易分析工作流
export function createTradingWorkflow() {
  const workflow = new StateGraph(TradingAgentAnnotation)
    // 阶段 1: 数据收集
    .addNode('data_collection', dataCollectionNode)

    // 阶段 2: 并行分析师
    .addNode('fundamental_analyst', fundamentalAnalystNode)
    .addNode('technical_analyst', technicalAnalystNode)
    .addNode('sentiment_analyst', sentimentAnalystNode)
    .addNode('news_analyst', newsAnalystNode)

    // 阶段 3: 综合分析
    .addNode('summarize', summarizeAnalysisNode)

    // 阶段 4: 研究团队协调
    .addNode('research_coordinator', researchTeamCoordinatorNode)

    // 阶段 5: 并行研究员
    .addNode('bull_researcher', bullResearcherNode)
    .addNode('bear_researcher', bearResearcherNode)

    // 阶段 6: 辩论主持
    .addNode('debate_moderator', debateModeratorNode)

    // 阶段 7: 共识评估
    .addNode('consensus_evaluator', consensusEvaluatorNode)

    // 阶段 8: 风险评估 (使用专业的风险管理师智能体)
    .addNode('risk_manager', riskManagerNode)

    // 阶段 9: 最终决策
    .addNode('agent', callModel)
    .addNode('tools', toolNode);

  // 定义工作流路径
  // 从起点开始数据收集
  workflow.addEdge('__start__', 'data_collection');

  // 数据收集完成后，并行启动所有分析师
  workflow.addEdge('data_collection', 'fundamental_analyst');
  workflow.addEdge('data_collection', 'technical_analyst');
  workflow.addEdge('data_collection', 'sentiment_analyst');
  workflow.addEdge('data_collection', 'news_analyst');

  // 所有分析师完成后，进入综合阶段
  workflow.addEdge(
    ['fundamental_analyst', 'technical_analyst', 'sentiment_analyst', 'news_analyst'],
    'summarize'
  );

  // 综合分析完成后，启动研究团队协调
  workflow.addEdge('summarize', 'research_coordinator');

  // 研究团队协调完成后，并行启动多头和空头研究员
  workflow.addEdge('research_coordinator', 'bull_researcher');
  workflow.addEdge('research_coordinator', 'bear_researcher');

  // 多头和空头研究完成后，启动辩论主持
  workflow.addEdge(['bull_researcher', 'bear_researcher'], 'debate_moderator');

  // 辩论完成后，进行共识评估或返回继续辩论
  workflow.addConditionalEdges('debate_moderator', routeDebate);

  // 共识评估完成后，进行风险评估
  workflow.addEdge('consensus_evaluator', 'risk_manager');

  // 风险评估完成后，进行最终决策
  workflow.addEdge('risk_manager', 'agent');

  // 保留最终决策节点的工具调用循环
  workflow.addEdge('tools', 'agent');
  workflow.addConditionalEdges('agent', shouldContinue);

  const checkpointer = new MemorySaver();
  return workflow.compile({ checkpointer });
}
