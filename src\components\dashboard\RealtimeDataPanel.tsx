'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { tradingApi } from '@/lib/services/analysis-service';

interface RealtimeDataPanelProps {
  ticker: string;
  analysisDate: string;
}

export function RealtimeDataPanel({ ticker, analysisDate }: RealtimeDataPanelProps) {
  const [activeDataType, setActiveDataType] = useState('stock');
  const [refreshInterval, setRefreshInterval] = useState(30000);

  // 获取股票数据
  const {
    data: stockData,
    isLoading: stockLoading,
    error: stockError,
  } = useQuery({
    queryKey: ['stockData', ticker, analysisDate],
    queryFn: () => tradingApi.getStockData(ticker, analysisDate),
    refetchInterval: refreshInterval,
    staleTime: 10000,
  });

  // 获取新闻数据
  const { data: newsData, isLoading: newsLoading } = useQuery({
    queryKey: ['newsData', ticker, analysisDate],
    queryFn: () => tradingApi.getNewsData(ticker, analysisDate),
    refetchInterval: 60000,
    staleTime: 30000,
  });

  // 获取技术指标数据
  const { data: technicalData, isLoading: technicalLoading } = useQuery({
    queryKey: ['technicalData', ticker, analysisDate],
    queryFn: () => tradingApi.getTechnicalIndicators(ticker, analysisDate),
    refetchInterval: refreshInterval,
    staleTime: 15000,
  });

  // 获取基本面数据
  const { data: fundamentalsData, isLoading: fundamentalsLoading } = useQuery({
    queryKey: ['fundamentalsData', ticker, analysisDate],
    queryFn: () => tradingApi.getFundamentalsData(ticker, analysisDate),
    refetchInterval: 300000,
    staleTime: 120000,
  });

  const dataTypes = [
    { id: 'stock', name: '股价数据', icon: '📈', color: 'blue' },
    { id: 'technical', name: '技术指标', icon: '📊', color: 'purple' },
    { id: 'news', name: '新闻数据', icon: '📰', color: 'green' },
    { id: 'fundamentals', name: '基本面', icon: '📋', color: 'orange' },
  ];

  const refreshIntervals = [
    { value: 10000, label: '10秒' },
    { value: 30000, label: '30秒' },
    { value: 60000, label: '1分钟' },
    { value: 300000, label: '5分钟' },
  ];

  const renderStockData = () => {
    if (stockLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (stockError || !stockData) {
      return (
        <div className="text-center text-slate-500 py-8">
          <p>暂无股价数据</p>
          {stockError && (
            <p className="text-sm text-red-500 mt-2">
              错误: {stockError instanceof Error ? stockError.message : '数据获取失败'}
            </p>
          )}
        </div>
      );
    }

    // 模拟数据结构
    const mockPriceHistory = Array.from({ length: 20 }, (_, i) => ({
      time: new Date(Date.now() - (19 - i) * 60000).toISOString(),
      price: 100 + Math.random() * 20 - 10,
      volume: Math.floor(Math.random() * 1000000) + 500000,
    }));

    const currentPrice = mockPriceHistory[mockPriceHistory.length - 1]?.price || 100;
    const previousPrice = mockPriceHistory[mockPriceHistory.length - 2]?.price || 100;
    const change = currentPrice - previousPrice;
    const changePercent = (change / previousPrice) * 100;

    return (
      <div className="space-y-6">
        {/* 实时刷新控制 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-slate-900 dark:text-white">实时股价数据</h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-slate-600 dark:text-slate-400">刷新间隔:</span>
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="text-sm border border-slate-300 dark:border-slate-600 rounded px-2 py-1 bg-white dark:bg-slate-800"
            >
              {refreshIntervals.map((interval) => (
                <option key={interval.value} value={interval.value}>
                  {interval.label}
                </option>
              ))}
            </select>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-600">实时</span>
            </div>
          </div>
        </div>

        {/* 当前价格信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <motion.div
            className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg"
            whileHover={{ scale: 1.02 }}
          >
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              ${currentPrice.toFixed(2)}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">当前价格</div>
            <div className="text-xs text-slate-500 mt-1">{new Date().toLocaleTimeString()}</div>
          </motion.div>
          <motion.div
            className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg"
            whileHover={{ scale: 1.02 }}
          >
            <div
              className={`text-2xl font-bold ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}
            >
              {change >= 0 ? '+' : ''}
              {change.toFixed(2)}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">涨跌额</div>
            <div className={`text-xs mt-1 ${change >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {change >= 0 ? '📈' : '📉'}
            </div>
          </motion.div>
          <motion.div
            className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg"
            whileHover={{ scale: 1.02 }}
          >
            <div
              className={`text-2xl font-bold ${
                changePercent >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {changePercent >= 0 ? '+' : ''}
              {changePercent.toFixed(2)}%
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">涨跌幅</div>
            <div className="text-xs text-slate-500 mt-1">日内变化</div>
          </motion.div>
          <motion.div
            className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg"
            whileHover={{ scale: 1.02 }}
          >
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {mockPriceHistory[mockPriceHistory.length - 1]?.volume.toLocaleString()}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">成交量</div>
            <div className="text-xs text-slate-500 mt-1">量比: 1.23</div>
          </motion.div>
        </div>

        {/* 更多市场数据 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-lg font-bold text-blue-600">
              ${Math.max(...mockPriceHistory.map((d) => d.price)).toFixed(2)}
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">今日最高</div>
          </div>
          <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div className="text-lg font-bold text-red-600">
              ${Math.min(...mockPriceHistory.map((d) => d.price)).toFixed(2)}
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">今日最低</div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div className="text-lg font-bold text-purple-600">
              ${mockPriceHistory[0]?.price.toFixed(2)}
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">开盘价</div>
          </div>
          <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div className="text-lg font-bold text-orange-600">
              ${(mockPriceHistory[0]?.price - 2).toFixed(2)}
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">昨收价</div>
          </div>
        </div>

        {/* 价格走势图 */}
        <div className="h-80">
          <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">价格走势</h4>
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={mockPriceHistory}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="time"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis tick={{ fontSize: 12 }} tickFormatter={(value) => `$${value.toFixed(2)}`} />
              <Tooltip
                formatter={(value: any) => [`$${value.toFixed(2)}`, '价格']}
                labelFormatter={(label) => `时间: ${new Date(label).toLocaleString()}`}
                contentStyle={{
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                }}
              />
              <Area
                type="monotone"
                dataKey="price"
                stroke="#3b82f6"
                fill="url(#colorPrice)"
                strokeWidth={2}
              />
              <defs>
                <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05} />
                </linearGradient>
              </defs>
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* 成交量图 */}
        <div className="h-48">
          <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">成交量</h4>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={mockPriceHistory}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="time"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
              />
              <Tooltip
                formatter={(value: any) => [value.toLocaleString(), '成交量']}
                labelFormatter={(label) => `时间: ${new Date(label).toLocaleString()}`}
              />
              <Bar dataKey="volume" fill="#10b981" opacity={0.7} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  const renderTechnicalData = () => {
    if (technicalLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      );
    }

    // 模拟技术指标数据
    const mockIndicators = {
      rsi: 65.4,
      macd: 1.23,
      signal: 0.89,
      histogram: 0.34,
      ma5: 102.5,
      ma10: 101.2,
      ma20: 99.8,
      ma60: 98.5,
      bollinger_upper: 105.2,
      bollinger_middle: 100.0,
      bollinger_lower: 94.8,
      volume_ma: 750000,
    };

    const mockChartData = Array.from({ length: 20 }, (_, i) => ({
      time: new Date(Date.now() - (19 - i) * 60000).toISOString(),
      rsi: 50 + Math.random() * 40,
      macd: Math.random() * 4 - 2,
      signal: Math.random() * 4 - 2,
      price: 100 + Math.random() * 20 - 10,
      ma5: 100 + Math.random() * 15 - 7.5,
      ma20: 100 + Math.random() * 10 - 5,
    }));

    return (
      <div className="space-y-6">
        {/* 技术指标概览 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div
              className={`text-2xl font-bold ${
                mockIndicators.rsi > 70
                  ? 'text-red-600'
                  : mockIndicators.rsi < 30
                  ? 'text-green-600'
                  : 'text-purple-600'
              }`}
            >
              {mockIndicators.rsi.toFixed(1)}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">RSI</div>
            <div className="text-xs text-slate-500 mt-1">
              {mockIndicators.rsi > 70 ? '超买' : mockIndicators.rsi < 30 ? '超卖' : '正常'}
            </div>
          </div>
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div
              className={`text-2xl font-bold ${
                mockIndicators.macd > mockIndicators.signal ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {mockIndicators.macd.toFixed(2)}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">MACD</div>
            <div className="text-xs text-slate-500 mt-1">
              {mockIndicators.macd > mockIndicators.signal ? '金叉' : '死叉'}
            </div>
          </div>
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{mockIndicators.ma5.toFixed(2)}</div>
            <div className="text-sm text-slate-600 dark:text-slate-400">MA5</div>
            <div className="text-xs text-slate-500 mt-1">5日均线</div>
          </div>
          <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {mockIndicators.ma20.toFixed(2)}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">MA20</div>
            <div className="text-xs text-slate-500 mt-1">20日均线</div>
          </div>
        </div>

        {/* RSI 指标图 */}
        <div className="h-64">
          <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
            RSI 相对强弱指标
          </h4>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={mockChartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="time"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis domain={[0, 100]} tick={{ fontSize: 12 }} />
              <Tooltip
                formatter={(value: any) => [value.toFixed(2), 'RSI']}
                labelFormatter={(label) => `时间: ${new Date(label).toLocaleString()}`}
              />
              <Line type="monotone" dataKey="rsi" stroke="#8b5cf6" strokeWidth={2} dot={false} />
              {/* 超买超卖线 */}
              <Line
                type="monotone"
                dataKey={() => 70}
                stroke="#ef4444"
                strokeDasharray="5 5"
                dot={false}
              />
              <Line
                type="monotone"
                dataKey={() => 30}
                stroke="#22c55e"
                strokeDasharray="5 5"
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* MACD 指标图 */}
        <div className="h-64">
          <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">MACD 指标</h4>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={mockChartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="time"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip
                formatter={(value: any, name: string) => [value.toFixed(3), name]}
                labelFormatter={(label) => `时间: ${new Date(label).toLocaleString()}`}
              />
              <Line
                type="monotone"
                dataKey="macd"
                stroke="#3b82f6"
                strokeWidth={2}
                name="MACD"
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="signal"
                stroke="#ef4444"
                strokeWidth={2}
                name="Signal"
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* 移动平均线 */}
        <div className="h-64">
          <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">移动平均线</h4>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={mockChartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="time"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => new Date(value).toLocaleTimeString()}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip
                formatter={(value: any, name: string) => [`$${value.toFixed(2)}`, name]}
                labelFormatter={(label) => `时间: ${new Date(label).toLocaleString()}`}
              />
              <Line
                type="monotone"
                dataKey="price"
                stroke="#1f2937"
                strokeWidth={2}
                name="价格"
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="ma5"
                stroke="#22c55e"
                strokeWidth={1.5}
                name="MA5"
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="ma20"
                stroke="#f59e0b"
                strokeWidth={1.5}
                name="MA20"
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  const renderNewsData = () => {
    if (newsLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        </div>
      );
    }

    // 模拟新闻数据
    const mockNews = [
      {
        id: 1,
        title: `${ticker} 公司发布第三季度财报，营收超预期`,
        summary: '公司第三季度营收达到预期的110%，净利润同比增长15%，显示出强劲的增长势头。',
        source: '财经日报',
        publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        sentiment: 'positive',
        impact: 'high',
        url: '#',
      },
      {
        id: 2,
        title: '行业分析师上调目标价至120美元',
        summary: '多家投行分析师基于强劲的基本面表现，将目标价从110美元上调至120美元。',
        source: '投资周刊',
        publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        sentiment: 'positive',
        impact: 'medium',
        url: '#',
      },
      {
        id: 3,
        title: '市场担忧供应链问题可能影响未来业绩',
        summary: '由于全球供应链紧张，市场担心可能对公司未来几个季度的业绩产生负面影响。',
        source: '商业观察',
        publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        sentiment: 'negative',
        impact: 'medium',
        url: '#',
      },
      {
        id: 4,
        title: '新产品发布会获得市场积极反响',
        summary: '公司最新产品发布会展示了创新技术，获得了行业专家和投资者的积极评价。',
        source: '科技前沿',
        publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        sentiment: 'positive',
        impact: 'low',
        url: '#',
      },
    ];

    const sentimentData = [
      { name: '积极', value: 60, color: '#22c55e' },
      { name: '中性', value: 25, color: '#6b7280' },
      { name: '消极', value: 15, color: '#ef4444' },
    ];

    return (
      <div className="space-y-6">
        {/* 新闻情绪分析 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
              新闻情绪分布
            </h4>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={sentimentData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {sentimentData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}%`, '占比']} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div>
            <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">情绪统计</h4>
            <div className="space-y-3">
              {sentimentData.map((item) => (
                <div key={item.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: item.color }}
                    ></div>
                    <span className="text-sm text-slate-700 dark:text-slate-300">{item.name}</span>
                  </div>
                  <span className="text-sm font-medium text-slate-900 dark:text-white">
                    {item.value}%
                  </span>
                </div>
              ))}
            </div>
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
                整体情绪: 积极
              </div>
              <div className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                基于过去24小时的新闻分析
              </div>
            </div>
          </div>
        </div>

        {/* 新闻列表 */}
        <div>
          <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">最新新闻</h4>
          <div className="space-y-4">
            {mockNews.map((article, index) => (
              <motion.div
                key={article.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 border border-slate-200 dark:border-slate-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-2">
                  <h5 className="font-medium text-slate-900 dark:text-white line-clamp-2 flex-1">
                    {article.title}
                  </h5>
                  <div className="flex items-center space-x-2 ml-4">
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        article.sentiment === 'positive'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : article.sentiment === 'negative'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                          : 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-400'
                      }`}
                    >
                      {article.sentiment === 'positive'
                        ? '积极'
                        : article.sentiment === 'negative'
                        ? '消极'
                        : '中性'}
                    </span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        article.impact === 'high'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                          : article.impact === 'medium'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                      }`}
                    >
                      {article.impact === 'high'
                        ? '高影响'
                        : article.impact === 'medium'
                        ? '中影响'
                        : '低影响'}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-3 mb-3">
                  {article.summary}
                </p>
                <div className="flex justify-between items-center text-xs text-slate-500">
                  <span>{article.source}</span>
                  <span>{new Date(article.publishedAt).toLocaleString()}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderFundamentalsData = () => {
    if (fundamentalsLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
        </div>
      );
    }

    // 模拟基本面数据
    const mockFundamentals = {
      pe: 18.5,
      pb: 2.3,
      roe: 0.15,
      roa: 0.08,
      debtToEquity: 0.45,
      currentRatio: 1.8,
      quickRatio: 1.2,
      grossMargin: 0.35,
      operatingMargin: 0.12,
      netMargin: 0.08,
      revenue: 5200000000,
      netIncome: 416000000,
      totalAssets: 8500000000,
      totalDebt: 2100000000,
      cashAndEquivalents: 1200000000,
      bookValue: 4800000000,
    };

    const ratioData = [
      { name: 'P/E', value: mockFundamentals.pe, benchmark: 20, color: '#3b82f6' },
      { name: 'P/B', value: mockFundamentals.pb, benchmark: 3, color: '#8b5cf6' },
      { name: 'ROE', value: mockFundamentals.roe * 100, benchmark: 12, color: '#22c55e' },
      { name: 'ROA', value: mockFundamentals.roa * 100, benchmark: 6, color: '#f59e0b' },
    ];

    return (
      <div className="space-y-6">
        {/* 估值指标 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div
              className={`text-2xl font-bold ${
                mockFundamentals.pe < 15
                  ? 'text-green-600'
                  : mockFundamentals.pe > 25
                  ? 'text-red-600'
                  : 'text-blue-600'
              }`}
            >
              {mockFundamentals.pe.toFixed(1)}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">市盈率 (P/E)</div>
            <div className="text-xs text-slate-500 mt-1">
              {mockFundamentals.pe < 15 ? '低估' : mockFundamentals.pe > 25 ? '高估' : '合理'}
            </div>
          </div>
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div
              className={`text-2xl font-bold ${
                mockFundamentals.pb < 1
                  ? 'text-green-600'
                  : mockFundamentals.pb > 3
                  ? 'text-red-600'
                  : 'text-purple-600'
              }`}
            >
              {mockFundamentals.pb.toFixed(1)}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">市净率 (P/B)</div>
            <div className="text-xs text-slate-500 mt-1">
              {mockFundamentals.pb < 1 ? '低估' : mockFundamentals.pb > 3 ? '高估' : '合理'}
            </div>
          </div>
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div
              className={`text-2xl font-bold ${
                mockFundamentals.roe > 0.15
                  ? 'text-green-600'
                  : mockFundamentals.roe < 0.08
                  ? 'text-red-600'
                  : 'text-green-500'
              }`}
            >
              {(mockFundamentals.roe * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">净资产收益率</div>
            <div className="text-xs text-slate-500 mt-1">
              {mockFundamentals.roe > 0.15 ? '优秀' : mockFundamentals.roe < 0.08 ? '较差' : '良好'}
            </div>
          </div>
          <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div
              className={`text-2xl font-bold ${
                mockFundamentals.roa > 0.08
                  ? 'text-green-600'
                  : mockFundamentals.roa < 0.04
                  ? 'text-red-600'
                  : 'text-orange-600'
              }`}
            >
              {(mockFundamentals.roa * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400">总资产收益率</div>
            <div className="text-xs text-slate-500 mt-1">
              {mockFundamentals.roa > 0.08 ? '优秀' : mockFundamentals.roa < 0.04 ? '较差' : '良好'}
            </div>
          </div>
        </div>

        {/* 财务比率对比 */}
        <div className="h-64">
          <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-4">财务比率对比</h4>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={ratioData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis dataKey="name" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip
                formatter={(value: any, name: string) => [
                  name === 'value'
                    ? `${value.toFixed(2)}${
                        ratioData.find((d) => d.name === name)?.name.includes('%') ? '%' : ''
                      }`
                    : value,
                  name === 'value' ? '当前值' : '行业平均',
                ]}
              />
              <Bar dataKey="value" fill="#3b82f6" name="当前值" />
              <Bar dataKey="benchmark" fill="#94a3b8" name="行业平均" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* 财务报表摘要 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <h5 className="font-medium text-slate-900 dark:text-white mb-3">收入状况</h5>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">总收入</span>
                <span className="text-sm font-medium">
                  ${(mockFundamentals.revenue / 1e9).toFixed(1)}B
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">净利润</span>
                <span className="text-sm font-medium">
                  ${(mockFundamentals.netIncome / 1e6).toFixed(0)}M
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">毛利率</span>
                <span className="text-sm font-medium">
                  {(mockFundamentals.grossMargin * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">净利率</span>
                <span className="text-sm font-medium">
                  {(mockFundamentals.netMargin * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
          <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <h5 className="font-medium text-slate-900 dark:text-white mb-3">资产负债</h5>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">总资产</span>
                <span className="text-sm font-medium">
                  ${(mockFundamentals.totalAssets / 1e9).toFixed(1)}B
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">总负债</span>
                <span className="text-sm font-medium">
                  ${(mockFundamentals.totalDebt / 1e9).toFixed(1)}B
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">账面价值</span>
                <span className="text-sm font-medium">
                  ${(mockFundamentals.bookValue / 1e9).toFixed(1)}B
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">负债权益比</span>
                <span className="text-sm font-medium">
                  {mockFundamentals.debtToEquity.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
          <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <h5 className="font-medium text-slate-900 dark:text-white mb-3">流动性</h5>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">现金及等价物</span>
                <span className="text-sm font-medium">
                  ${(mockFundamentals.cashAndEquivalents / 1e9).toFixed(1)}B
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">流动比率</span>
                <span className="text-sm font-medium">
                  {mockFundamentals.currentRatio.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">速动比率</span>
                <span className="text-sm font-medium">
                  {mockFundamentals.quickRatio.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">营运利润率</span>
                <span className="text-sm font-medium">
                  {(mockFundamentals.operatingMargin * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (activeDataType) {
      case 'stock':
        return renderStockData();
      case 'technical':
        return renderTechnicalData();
      case 'news':
        return renderNewsData();
      case 'fundamentals':
        return renderFundamentalsData();
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* 数据类型选择 */}
      <div className="flex flex-wrap gap-2">
        {dataTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => setActiveDataType(type.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all ${
              activeDataType === type.id
                ? `bg-${type.color}-600 text-white shadow-lg`
                : `bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-${type.color}-50 dark:hover:bg-${type.color}-900/20`
            }`}
          >
            <span>{type.icon}</span>
            <span>{type.name}</span>
            {activeDataType === type.id && (
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            )}
          </button>
        ))}
      </div>

      {/* 数据内容 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              {dataTypes.find((t) => t.id === activeDataType)?.name} - {ticker}
            </span>
            <div className="flex items-center space-x-2 text-sm text-slate-500">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>实时更新</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <motion.div
            key={activeDataType}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {renderContent()}
          </motion.div>
        </CardContent>
      </Card>
    </div>
  );
}
