'use client';

import { AnalysisStatus } from '@/hooks/useAnalysisStatus';
import {  Workflow } from '@/types/database';
import { useState } from 'react';

interface AnalysisActionButtonProps {
  task: Workflow;
  analysisStatus?: AnalysisStatus;
  onStartAnalysis?: (task: Workflow) => Promise<void>;
  onViewAnalysis?: (task: Workflow) => Promise<void>;
  startingTask?: string | null;
  loading?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function AnalysisActionButton({
  task,
  analysisStatus,
  onStartAnalysis,
  onViewAnalysis,
  startingTask,
  loading = false,
  className = '',
  size = 'md',
}: AnalysisActionButtonProps) {
  const [isProcessing, setIsProcessing] = useState(false);

  // 判断按钮类型和状态
  const getButtonConfig = () => {
    // 如果任务状态是pending，显示开始分析按钮
    if (task.status === 'pending') {
      return {
        type: 'start',
        text: '开始分析',
        loadingText: '启动中...',
        disabled: startingTask === task.workflow_id || loading || isProcessing,
        className: 'bg-green-600 hover:bg-green-700 disabled:bg-green-400',
        ariaLabel: `开始分析任务 ${task.ticker}`,
      };
    }

    // 如果任务失败，显示重新开始按钮
    if (task.status === 'failed') {
      return {
        type: 'start', // 复用 'start' 类型逻辑
        text: '重新开始',
        loadingText: '正在重启...',
        disabled: startingTask === task.workflow_id || loading || isProcessing,
        className: 'bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-400',
        ariaLabel: `重新开始分析任务 ${task.ticker}`,
      };
    }

    // 如果任务正在运行或已完成，显示查看分析按钮
    if (task.status === 'running' || task.status === 'completed' || analysisStatus) {
      const isRunning = task.status === 'running' || analysisStatus?.status === 'running';
      return {
        type: 'view',
        text: isRunning ? '实时查看' : '查看分析',
        loadingText: '加载中...',
        disabled: loading || isProcessing,
        className: 'bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400',
        ariaLabel: `${isRunning ? '实时查看' : '查看分析'}任务 ${task.ticker} 的分析结果`,
      };
    }

    // 其他状态不显示按钮
    return null;
  };

  const buttonConfig = getButtonConfig();

  // 如果不需要显示按钮，返回null
  if (!buttonConfig) {
    return null;
  }

  // 处理按钮点击
  const handleClick = async () => {
    if (buttonConfig.disabled) return;
    setIsProcessing(true);
    try {
      if (buttonConfig.type === 'start' && onStartAnalysis) {
        await onStartAnalysis(task);
      } else if (buttonConfig.type === 'view' && onViewAnalysis) {
        await onViewAnalysis(task);
      }
    } catch (error) {
      console.error('Button action failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // 判断是否显示加载状态
  const showLoading =
    buttonConfig.disabled && (startingTask === task.workflow_id || loading || isProcessing);

  // 获取尺寸样式
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-3 py-1.5 text-sm';
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={buttonConfig.disabled}
      className={`inline-flex items-center ${getSizeClasses()} font-medium text-white rounded-md transition-colors disabled:cursor-not-allowed ${buttonConfig.className} ${className}`}
      aria-label={buttonConfig.ariaLabel}
      type="button"
    >
      {showLoading ? (
        <>
          <svg
            className="animate-spin -ml-1 mr-2 h-3 w-3 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {buttonConfig.loadingText}
        </>
      ) : (
        buttonConfig.text
      )}
    </button>
  );
}
