import { getCurrentUser } from '@/lib/auth';
import LangGraphDatabase from '@/lib/langgraph-database';
import { WorkflowStatus } from '@/types/langgraph-database';
import { create } from 'lodash';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();
    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const ticker = searchParams.get('ticker') || undefined;
    const statusParam = searchParams.get('status');
    const dateFromParam = searchParams.get('date_from');
    const dateToParam = searchParams.get('date_to');

    // 处理状态参数
    let status: WorkflowStatus[] | undefined;
    if (statusParam) {
      status = statusParam.split(',') as WorkflowStatus[];
    }

    // 处理日期参数
    let dateFrom: Date | undefined;
    let dateTo: Date | undefined;
    if (dateFromParam) {
      dateFrom = new Date(dateFromParam);
    }
    if (dateToParam) {
      dateTo = new Date(dateToParam);
    }

    // 构建查询选项
    const options = {
      created_by: currentUser?.userId,
      ticker,
      status,
      date_from: dateFrom,
      date_to: dateTo,
      limit,
      offset: (page - 1) * limit,
    };

    // 查询工作流历史
    const workflows = await LangGraphDatabase.queryAnalysisWorkflows(options);

    // 获取总数（用于分页）
    const totalOptions = { ...options };
    const { limit: _, offset: __, ...totalOptionsWithoutPagination } = totalOptions;
    const allWorkflows = await LangGraphDatabase.queryAnalysisWorkflows(
      totalOptionsWithoutPagination
    );
    const total = allWorkflows.length;

    const result = {
      workflows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('获取分析历史失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '获取分析历史失败',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
