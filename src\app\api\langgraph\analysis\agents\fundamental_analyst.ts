import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AIMessage } from '@langchain/core/messages';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

const fundamentalAnalystPrompt = PromptTemplate.fromTemplate(`
你是一位资深的基本面分析师，拥有15年的A股市场分析经验。你的专长是深度挖掘公司的内在价值，识别被市场低估或高估的投资机会。

【分析任务】
请对以下公司进行全面的基本面分析：

公司代码: {ticker}
分析日期: {date}
分析深度: {analysisDepth}

【可用数据】
{fundamentalData}

【分析框架】
请按照以下专业框架进行分析：

1. **公司概况与商业模式**
   - 主营业务和收入结构
   - 商业模式和盈利模式
   - 核心竞争优势和护城河
   - 在产业链中的地位

2. **财务健康度分析**
   - 盈利能力：毛利率、净利率、ROE、ROA趋势
   - 营运能力：存货周转率、应收账款周转率
   - 偿债能力：资产负债率、流动比率、速动比率
   - 现金流分析：经营性现金流与净利润匹配度

3. **估值分析**
   - 相对估值：PE、PB、PS与行业对比
   - 绝对估值：DCF模型估算（如数据充足）
   - 估值合理性判断和安全边际

4. **成长性分析**
   - 历史增长轨迹（收入、利润、现金流）
   - 未来增长驱动因素
   - 行业增长空间和公司市场份额

5. **风险评估**
   - 财务风险：债务风险、流动性风险
   - 经营风险：行业周期、竞争加剧
   - 政策风险：监管变化、政策影响

6. **投资建议**
   - 综合评级：强烈推荐/推荐/中性/减持/卖出
   - 目标价位和持有期建议
   - 关键监控指标

【输出要求】
- 分析要客观、数据驱动，避免主观臆断
- 突出关键财务指标和风险点
- 提供明确的投资逻辑和建议
- 如果数据不足，请明确指出并说明影响
`);

export async function fundamentalAnalystNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, data, messages } = state;

  console.log(`[基本面分析师] 开始分析股票: ${ticker}`);

  if (config.selected_analysts && !config.selected_analysts.includes('fundamental')) {
    console.log(`[基本面分析师] 跳过基本面分析`);
    return { messages };
  }

  try {
    // 检查数据可用性
    if (!data.fundamentalData) {
      console.warn('[基本面分析师] 基本面数据不可用，使用历史价格数据进行简化分析');
      console.log(`[基本面分析师] 跳过基本面分析`);
      return { messages };
    }

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.nuwaapi.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM || 'gpt-4o',
      temperature: 0.1, // 降低温度以获得更一致的分析
      apiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 准备分析数据
    const analysisData = {
      fundamentalData: data.fundamentalData || '数据暂不可用，请基于股票代码进行一般性分析',
      technicalData: data.technicalData ? data.technicalData.slice(-5) : null, // 最近5天数据作为参考
    };

    const prompt = await fundamentalAnalystPrompt.format({
      ticker,
      date,
      analysisDepth: config.researchDepth || 'standard',
      fundamentalData: JSON.stringify(analysisData, null, 2),
    });

    console.log(`[基本面分析师] 正在调用LLM进行分析...`);
    const response = await llm.invoke(prompt);
    const analysisReport = response.content as string;

    // 提取关键指标和建议
    const keyMetrics = extractKeyMetrics(analysisReport);
    const investmentRating = extractInvestmentRating(analysisReport);

    // 生成结构化摘要
    const summary = generateSummary(analysisReport, keyMetrics, investmentRating);

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【基本面分析师报告】\n\n${analysisReport}`,
        name: 'FundamentalAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      fundamental: {
        summary,
        report: analysisReport,
        keyMetrics,
        investmentRating,
        analyst: 'FundamentalAnalyst',
        timestamp: new Date().toISOString(),
        confidence: calculateConfidence(data.fundamentalData),
      },
    };

    console.log(`[基本面分析师] 分析完成，评级: ${investmentRating}`);
    return {
      messages: newMessages,
      analysis,
      currentStage: 'fundamental_analysis_completed',
      progress: Math.min(state.progress + 20, 100),
    };
  } catch (error) {
    console.error('[基本面分析师] 分析失败:', error);
    const errorMessage = `基本面分析失败: ${
      error instanceof Error ? error.message : String(error)
    }`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【基本面分析师】${errorMessage}`,
        name: 'FundamentalAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      fundamental: {
        summary: '基本面分析失败',
        report: errorMessage,
        error: true,
        analyst: 'FundamentalAnalyst',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, analysis };
  }
}

// 辅助函数：提取关键指标
function extractKeyMetrics(report: string): Record<string, any> {
  const metrics: Record<string, any> = {};

  // 使用正则表达式提取常见财务指标
  const patterns = {
    pe: /市盈率[：:]\s*([0-9.]+)/i,
    pb: /市净率[：:]\s*([0-9.]+)/i,
    roe: /ROE[：:]\s*([0-9.%]+)/i,
    debt_ratio: /资产负债率[：:]\s*([0-9.%]+)/i,
    gross_margin: /毛利率[：:]\s*([0-9.%]+)/i,
  };

  for (const [key, pattern] of Object.entries(patterns)) {
    const match = report.match(pattern);
    if (match) {
      metrics[key] = match[1];
    }
  }

  return metrics;
}

// 辅助函数：提取投资评级
function extractInvestmentRating(report: string): string {
  const ratingPatterns = [/强烈推荐/i, /推荐/i, /中性/i, /减持/i, /卖出/i];

  for (const pattern of ratingPatterns) {
    if (pattern.test(report)) {
      return pattern.source.replace(/[\/\\i]/g, '');
    }
  }

  return '中性'; // 默认评级
}

// 辅助函数：生成摘要
function generateSummary(report: string, keyMetrics: Record<string, any>, rating: string): string {
  const lines = report.split('\n').filter((line) => line.trim());
  const firstParagraph = lines.slice(0, 3).join(' ');

  return `${rating} - ${firstParagraph.substring(0, 150)}...`;
}

// 辅助函数：计算置信度
function calculateConfidence(fundamentalData: any): number {
  if (!fundamentalData || fundamentalData === '数据暂不可用，请基于股票代码进行一般性分析') {
    return 0.3; // 数据不足时置信度较低
  }

  // 根据数据完整性计算置信度
  if (Array.isArray(fundamentalData) && fundamentalData.length > 0) {
    return 0.8;
  }

  return 0.6; // 中等置信度
}
