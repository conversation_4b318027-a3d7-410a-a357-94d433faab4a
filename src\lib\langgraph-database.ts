// LangGraph Workflow Database Operations - V4.0 (Workflow-centric)
import mysql from 'mysql2/promise';
import {
  // Keep necessary request/response types, but many will need to be updated/refactored
  // This requires a corresponding update in `src/types/langgraph-database.ts`
  // For now, we assume they are updated.
  CreateWorkflowRequest,
  LogWorkflowEventRequest,
  SaveAnalystReportRequest, // Renamed and redesigned
  SaveConsensusEvaluationRequest, // New type for consensus
  SaveFinalDecisionRequest, // Renamed and redesigned
  SaveResearchReportRequest, // Renamed and redesigned
  SaveRiskAssessmentRequest, // New type for risk assessment
  SaveStateSnapshotRequest,
  UpdateWorkflowStatusRequest,
  Workflow, // New type
  WorkflowQueryOptions,
} from '../types/langgraph-database';
import { dbConfig } from './db-config';

// Create a connection pool
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

export class LangGraphDatabase {
  // ============================================================================
  // Private Utility Methods
  // ============================================================================

  protected static async withConnection<T>(
    operation: (connection: mysql.PoolConnection) => Promise<T>
  ): Promise<T> {
    const connection = await pool.getConnection();
    try {
      return await operation(connection);
    } finally {
      connection.release();
    }
  }

  protected static generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private static safeJsonStringify(obj: any): string | null {
    if (obj === undefined || obj === null) {
      return null;
    }
    try {
      return JSON.stringify(obj);
    } catch (error) {
      console.error('Failed to stringify object:', error);
      return null;
    }
  }

  // ============================================================================
  // 1. Workflow Instance Management
  // ============================================================================

  static async createWorkflow(request: CreateWorkflowRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const workflowId = this.generateId('wf');
      await connection.execute('CALL CreateWorkflow(?, ?, ?, ?, ?, ?)', [
        workflowId,
        request.ticker || '',
        request.title || '',
        request.description || null,
        this.safeJsonStringify(request.config) || null,
        request.created_by || 'system',
      ]);
      return workflowId;
    });
  }

  static async updateWorkflowStatus(request: UpdateWorkflowStatusRequest): Promise<void> {
    await this.withConnection(async (connection) => {
      // Use direct UPDATE instead of stored procedure to avoid collation issues
      await connection.execute(
        `UPDATE workflows SET
         current_stage = ?,
         progress = ?,
         status = ?,
         error_message = ?,
         updated_at = NOW(),
         started_at = CASE WHEN started_at IS NULL AND ? = 'running' THEN NOW() ELSE started_at END,
         completed_at = CASE WHEN ? IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE NULL END
         WHERE workflow_id = ?`,
        [
          request.current_stage || '',
          request.progress || 0,
          request.status || 'pending',
          request.error_message || null,
          request.status || 'pending', // For started_at condition
          request.status || 'pending', // For completed_at condition
          request.workflow_id || '',
        ]
      );
    });
  }

  static async getWorkflow(workflow_id: string): Promise<Workflow | null> {
    return this.withConnection(async (connection) => {
      const [rows] = await connection.execute('SELECT * FROM workflows WHERE workflow_id = ?', [
        workflow_id,
      ]);
      const results = rows as Workflow[];
      return results.length > 0 ? results[0] : null;
    });
  }

  static async queryAnalysisWorkflows(options: WorkflowQueryOptions = {}): Promise<Workflow[]> {
    return this.withConnection(async (connection) => {
      // Base query with a subquery to aggregate analysis reports
      let query = `
        SELECT 
          wo.*,
          (
            SELECT JSON_OBJECTAGG(ar.analyst_type, SUBSTRING_INDEX(ar.summary, ' - ', 1))
            FROM analyst_reports ar
            WHERE ar.workflow_id = wo.workflow_id 
          ) as analysis_summary
        FROM workflow_overview wo
        WHERE 1 = 1
      `;
      const params: any[] = [];

      // Handle status filtering: default to 'completed' if not provided
      if (options.status && options.status.length > 0) {
        query += ` AND wo.status IN (${options.status.map(() => '?').join(',')})`;
        params.push(...options.status);
      }

      if (options.ticker) {
        query += ' AND wo.ticker = ?';
        params.push(options.ticker);
      }
      if (options.date_from) {
        query += ' AND wo.created_at >= ?';
        params.push(options.date_from);
      }
      if (options.date_to) {
        query += ' AND wo.created_at <= ?';
        params.push(options.date_to);
      }
      if (options.created_by) {
        query += ' AND wo.created_by = ?';
        params.push(options.created_by);
      }

      query += ' ORDER BY wo.created_at DESC';

      // Handle LIMIT and OFFSET
      if (options.limit && options.limit > 0) {
        const limitValue = Math.max(1, Math.min(1000, parseInt(String(options.limit))));
        query += ` LIMIT ${limitValue}`;
      }
      if (options.offset && options.offset > 0) {
        const offsetValue = Math.max(0, parseInt(String(options.offset)));
        query += ` OFFSET ${offsetValue}`;
      }

      try {
        const [rows] = await connection.execute(query, params);

        // Parse the JSON string for analysis_summary
        const results = (rows as any[]).map((row) => {
          if (row.analysis_summary && typeof row.analysis_summary === 'string') {
            try {
              row.analysis_summary = JSON.parse(row.analysis_summary);
            } catch (e) {
              console.error(`Failed to parse analysis_summary for workflow ${row.workflow_id}`, e);
              row.analysis_summary = null; // Set to null if parsing fails
            }
          } else if (row.analysis_summary === null) {
            row.analysis_summary = {}; // Ensure it's an empty object if no reports found
          }
          return row;
        });

        return results as Workflow[];
      } catch (error: any) {
        if (error.code === 'ER_NO_SUCH_TABLE' || error.message?.includes('workflow_overview')) {
          console.warn('workflow_overview view not found, falling back to workflows table');
          const fallbackQuery = query
            .replace('workflow_overview wo', 'workflows wo')
            .replace(/wo\./g, '')
            .replace(
              /\(SELECT JSON_OBJECTAGG[\s\S]*?\) as analysis_summary/,
              'NULL as analysis_summary'
            );
          const [rows] = await connection.execute(fallbackQuery, params);
          return rows as Workflow[];
        }
        throw error;
      }
    });
  }
  static async queryWorkflows(options: WorkflowQueryOptions = {}): Promise<Workflow[]> {
    return this.withConnection(async (connection) => {
      let query = 'SELECT * FROM workflow_overview WHERE status = "completed"';
      const params: any[] = [];

      if (options.ticker) {
        query += ' AND ticker = ?';
        params.push(options.ticker);
      }
      if (options.status && options.status.length > 0) {
        query += ` AND status IN (${options.status.map(() => '?').join(',')})`;
        params.push(...options.status);
      }
      if (options.date_from) {
        query += ' AND created_at >= ?';
        params.push(options.date_from);
      }
      if (options.date_to) {
        query += ' AND created_at <= ?';
        params.push(options.date_to);
      }

      if (options.created_by) {
        query += ' AND created_by = ?';
        params.push(options.created_by);
      }

      query += ' ORDER BY created_at DESC';

      // Handle LIMIT and OFFSET with proper integer conversion and validation
      if (options.limit && options.limit > 0) {
        const limitValue = Math.max(1, Math.min(1000, parseInt(String(options.limit))));
        query += ` LIMIT ${limitValue}`;
      }
      if (options.offset && options.offset > 0) {
        const offsetValue = Math.max(0, parseInt(String(options.offset)));
        query += ` OFFSET ${offsetValue}`;
      }

      console.log('Executing query:', query, 'with params:', params);
      try {
        const [rows] = await connection.execute(query, params);
        return rows as Workflow[];
      } catch (error: any) {
        // If workflow_overview view doesn't exist, fall back to workflows table
        if (error.code === 'ER_NO_SUCH_TABLE' || error.message?.includes('workflow_overview')) {
          console.warn('workflow_overview view not found, falling back to workflows table');
          const fallbackQuery = query.replace('workflow_overview', 'workflows');
          const [rows] = await connection.execute(fallbackQuery, params);
          return rows as Workflow[];
        }
        throw error;
      }
    });
  }

  // ============================================================================
  // 2. Analyst & Research Report Management
  // ============================================================================

  static async saveAnalystReport(request: SaveAnalystReportRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        const reportId = this.generateId('rep');
        // 1. Insert into the main analyst_reports table
        const reportSql = `
          INSERT INTO analyst_reports (report_id, workflow_id, analyst_type, summary, status, execution_time_ms)
          VALUES (?, ?, ?, ?, ?, ?)
        `;
        await connection.execute(reportSql, [
          reportId,
          request.workflow_id,
          request.analyst_type,
          request.summary || null,
          request.status || 'completed',
          request.execution_time_ms || null,
        ]);

        // 2. Insert into the specific details table based on type
        let detailsSql = '';
        let detailsParams: any[] = [];

        switch (request.analyst_type) {
          case 'technical':
            const techDetails = request.details as any;
            detailsSql = `
              INSERT INTO technical_analysis_details (report_id, trading_signal, trend_signal, support_level, resistance_level, stop_loss_level, target_price, rsi_value, macd_signal, key_levels)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            detailsParams = [
              reportId,
              techDetails?.trading_signal || null,
              techDetails?.trend_signal || null,
              techDetails?.support_level || null,
              techDetails?.resistance_level || null,
              techDetails?.stop_loss_level || null,
              techDetails?.target_price || null,
              techDetails?.rsi_value || null,
              techDetails?.macd_signal || null,
              this.safeJsonStringify(techDetails?.key_levels) || null,
            ];
            break;
          case 'sentiment':
            const sentDetails = request.details as any;
            detailsSql = `
              INSERT INTO sentiment_analysis_details (report_id, overall_sentiment, sentiment_score, positive_news_count, negative_news_count, neutral_news_count, key_drivers)
              VALUES (?, ?, ?, ?, ?, ?, ?)
            `;
            detailsParams = [
              reportId,
              sentDetails?.overall_sentiment || 'neutral',
              sentDetails?.sentiment_score || 0,
              sentDetails?.positive_news_count || 0,
              sentDetails?.negative_news_count || 0,
              sentDetails?.neutral_news_count || 0,
              sentDetails?.key_drivers || null,
            ];
            break;
          // Add cases for 'news' and 'fundamental' if they have detail tables
        }

        if (detailsSql) {
          await connection.execute(detailsSql, detailsParams);
        }

        await connection.commit();
        return reportId;
      } catch (error) {
        await connection.rollback();
        console.error(`Failed to save analyst report for workflow ${request.workflow_id}:`, error);
        throw error;
      }
    });
  }

  static async saveResearchReport(request: SaveResearchReportRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        const reportId = this.generateId('res');
        // 1. Insert into the main research_reports table
        const reportSql = `
          INSERT INTO research_reports (report_id, workflow_id, researcher_type, summary, confidence_level, target_price, time_horizon, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;
        await connection.execute(reportSql, [
          reportId,
          request.workflow_id,
          request.researcher_type,
          request.summary || null,
          request.confidence_level || null,
          request.target_price || null,
          request.time_horizon || null,
          request.status || 'completed',
        ]);

        // 2. Insert arguments into research_arguments table
        if (request.arguments && request.arguments.length > 0) {
          const argumentSql = `
            INSERT INTO research_arguments (argument_id, report_id, parent_argument_id, argument_type, content, source, strength_score, sequence_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `;
          for (const arg of request.arguments) {
            const argumentId = this.generateId('arg');
            await connection.execute(argumentSql, [
              argumentId,
              reportId,
              arg.parent_argument_id || null,
              arg.argument_type || 'main_argument',
              arg.content || '',
              arg.source || null,
              arg.strength_score || null,
              arg.sequence_order || 0,
            ]);
          }
        }

        await connection.commit();
        return reportId;
      } catch (error) {
        await connection.rollback();
        console.error(`Failed to save research report for workflow ${request.workflow_id}:`, error);
        throw error;
      }
    });
  }

  // ... Methods to get analyst and research reports would be added here ...
  // These would involve JOINs between the main and details tables.

  // ============================================================================
  // 3. Consensus and Decision Management
  // ============================================================================

  static async saveConsensusEvaluation(request: SaveConsensusEvaluationRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const consensusId = this.generateId('con');
      const sql = `
        INSERT INTO consensus_evaluations (consensus_id, workflow_id, bull_strength, bear_strength, consensus_direction, consensus_confidence, synthesis_summary, key_agreement_points, key_disagreement_points)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      await connection.execute(sql, [
        consensusId,
        request.workflow_id,
        request.bull_strength || null,
        request.bear_strength || null,
        request.consensus_direction || null,
        request.consensus_confidence || null,
        request.synthesis_summary || null,
        this.safeJsonStringify(request.key_agreement_points) || null,
        this.safeJsonStringify(request.key_disagreement_points) || null,
      ]);
      return consensusId;
    });
  }

  static async saveRiskAssessment(request: SaveRiskAssessmentRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const riskId = this.generateId('risk');
      const sql = `
        INSERT INTO risk_assessments (risk_id, workflow_id, overall_risk_level, risk_score, summary, market_risk, liquidity_risk, credit_risk, operational_risk, scenario_analysis, risk_metrics, recommendations, risk_controls, risk_warnings, status, execution_time_ms)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      await connection.execute(sql, [
        riskId,
        request.workflow_id,
        request.overall_risk_level || 'medium',
        request.risk_score || 5,
        request.summary || null,
        this.safeJsonStringify(request.market_risk) || null,
        this.safeJsonStringify(request.liquidity_risk) || null,
        this.safeJsonStringify(request.credit_risk) || null,
        this.safeJsonStringify(request.operational_risk) || null,
        this.safeJsonStringify(request.scenario_analysis) || null,
        this.safeJsonStringify(request.risk_metrics) || null,
        this.safeJsonStringify(request.recommendations) || null,
        this.safeJsonStringify(request.risk_controls) || null,
        this.safeJsonStringify(request.risk_warnings) || null,
        request.status || 'completed',
        request.execution_time_ms || null,
      ]);
      return riskId;
    });
  }

  static async saveFinalDecision(request: SaveFinalDecisionRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const decisionId = this.generateId('dec');
      const sql = `
        INSERT INTO final_decisions (decision_id, workflow_id, decision_type, confidence_level, decision_rationale, entry_price_range, stop_loss_price, take_profit_price, position_size_percentage)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      await connection.execute(sql, [
        decisionId,
        request.workflow_id,
        request.decision_type || 'hold',
        request.confidence_level || null,
        request.decision_rationale || null,
        this.safeJsonStringify(request.entry_price_range) || null,
        request.stop_loss_price || null,
        request.take_profit_price || null,
        request.position_size_percentage || null,
      ]);
      return decisionId;
    });
  }

  // ... Methods for saving and getting debate sessions, rounds, and utterances ...

  // ============================================================================
  // 4. Event Logging and State Snapshots
  // ============================================================================

  static async logWorkflowEvent(request: LogWorkflowEventRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const eventId = this.generateId('evt');
      await connection.execute('CALL LogWorkflowEvent(?, ?, ?, ?, ?, ?)', [
        eventId,
        request.workflow_id || '',
        request.stage_name || null,
        request.event_type || 'log',
        request.content || '',
        this.safeJsonStringify(request.metadata) || null,
      ]);
      return eventId;
    });
  }

  static async saveStateSnapshot(request: SaveStateSnapshotRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      const snapshotId = this.generateId('snap');
      const sql = `
        INSERT INTO workflow_state_snapshots (snapshot_id, workflow_id, stage_name, state_data, checkpoint_id)
        VALUES (?, ?, ?, ?, ?)
      `;
      await connection.execute(sql, [
        snapshotId,
        request.workflow_id,
        request.stage_name,
        this.safeJsonStringify(request.state_data) || null,
        request.checkpoint_id || null,
      ]);
      return snapshotId;
    });
  }

  // ============================================================================
  // 4. Getter Methods for Comparison API
  // ============================================================================

  static async getAnalystReports(workflow_id: string): Promise<any[]> {
    return this.withConnection(async (connection) => {
      const sql = `
        SELECT ar.*, 
               tad.trading_signal, tad.trend_signal, tad.support_level, tad.resistance_level,
               sad.overall_sentiment, sad.sentiment_score,
               nad.impact_score, nad.key_news_summary
        FROM analyst_reports ar
        LEFT JOIN technical_analysis_details tad ON ar.report_id = tad.report_id
        LEFT JOIN sentiment_analysis_details sad ON ar.report_id = sad.report_id
        LEFT JOIN news_analysis_details nad ON ar.report_id = nad.report_id
        WHERE ar.workflow_id = ?
        ORDER BY ar.created_at
      `;
      const [rows] = await connection.execute(sql, [workflow_id]);
      return rows as any[];
    });
  }

  static async getResearchReports(workflow_id: string): Promise<any[]> {
    return this.withConnection(async (connection) => {
      const sql = `
        SELECT rr.*, 
               GROUP_CONCAT(ra.content SEPARATOR '\n---\n') as arguments_text
        FROM research_reports rr
        LEFT JOIN research_arguments ra ON rr.report_id = ra.report_id
        WHERE rr.workflow_id = ?
        GROUP BY rr.report_id
        ORDER BY rr.created_at
      `;
      const [rows] = await connection.execute(sql, [workflow_id]);
      return rows as any[];
    });
  }

  static async getRiskAssessment(workflow_id: string): Promise<any | null> {
    return this.withConnection(async (connection) => {
      const sql = `
        SELECT * FROM risk_assessments 
        WHERE workflow_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
      `;
      const [rows] = await connection.execute(sql, [workflow_id]);
      return Array.isArray(rows) && rows.length > 0 ? rows[0] : null;
    });
  }

  static async getFinalDecision(workflow_id: string): Promise<any | null> {
    return this.withConnection(async (connection) => {
      const sql = `
        SELECT * FROM final_decisions 
        WHERE workflow_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
      `;
      const [rows] = await connection.execute(sql, [workflow_id]);
      return Array.isArray(rows) && rows.length > 0 ? rows[0] : null;
    });
  }

  static async getWorkflowEvents(
    workflow_id: string,
    options: { limit?: number } = {}
  ): Promise<any[]> {
    return this.withConnection(async (connection) => {
      let sql = `
        SELECT * FROM workflow_events 
        WHERE workflow_id = ? 
        ORDER BY created_at DESC
      `;
      const params = [workflow_id];

      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit.toString());
      }

      const [rows] = await connection.execute(sql, params);
      return rows as any[];
    });
  }

  // ============================================================================
  // 5. System and Cleanup
  // ============================================================================

  static async closePool(): Promise<void> {
    await pool.end();
  }
}

export default LangGraphDatabase;
