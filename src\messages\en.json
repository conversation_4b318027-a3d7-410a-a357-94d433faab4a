{"site": {"title": "TradingAgents - AI-Powered Stock Analysis Platform", "description": "Professional AI-driven stock analysis platform providing comprehensive intelligent support for investment decisions", "keywords": "stock analysis,AI investment,smart investing,stock recommendations,investment decisions,multi-agent,large language models"}, "navigation": {"home": "Home", "createTask": "Create Task", "tasks": "Tasks", "login": "<PERSON><PERSON>", "register": "Register", "about": "About Us", "features": "Features", "examples": "Examples", "docs": "Documentation", "taskList": "Task List", "welcome": "Welcome", "profile": "Profile", "logout": "Logout", "quickStart": "Quick Start"}, "homepage": {"hero": {"title": "Intelligent Stock Analysis System", "subtitle": "Professional AI-driven stock analysis platform providing comprehensive intelligent support for investment decisions", "analysts": {"market": "Market Analyst", "news": "News Analyst", "social": "Social Media Analyst", "fundamental": "Fundamental Analyst", "risk": "Risk Management Team"}, "buttons": {"quickStart": "Quick Start", "viewTasks": "View Tasks"}}, "features": {"title": "Core Features", "subtitle": "Multi-dimensional intelligent analysis to support precise investment decisions with data-driven insights", "cards": {"customAnalysis": {"title": "Customized Analysis Tasks", "description": "Flexibly configure analysis parameters and AI agent teams according to your investment needs", "features": {"flexible": "• Flexible parameter configuration", "team": "• Professional AI agent teams", "depth": "• In-depth analysis reports"}}, "multiAgent": {"title": "Multi-Agent Collaboration", "description": "Simulate professional teams from real trading companies for multi-perspective in-depth analysis", "features": {"technical": "• Technical Analysts", "news": "• News Intelligence Analysts", "sentiment": "• Market Sentiment Analysts", "fundamental": "• Fundamental Analysts"}}, "realTimeTracking": {"title": "Real-time Analysis Monitoring", "description": "Track analysis progress in real-time and observe AI agent discussion processes", "features": {"progress": "• Real-time progress tracking", "discussion": "• AI agent discussion processes", "visualization": "• Visual data presentation"}}}}, "cases": {"title": "Success Case Studies", "subtitle": "Real analysis cases demonstrating the professional standards and investment value of AI intelligent analysis", "examples": {"aapl": {"company": "Apple Inc.", "title": "Apple Inc. In-depth Analysis", "description": "Comprehensive analysis based on iPhone 15 launch, service business growth, and AI technology deployment, multi-dimensional evaluation shows strong growth potential", "recommendation": "Buy Recommendation"}, "tsla": {"company": "Tesla", "title": "Tesla Risk Assessment", "description": "Dual impact of autonomous driving technology progress and intensified competition, need to closely monitor capacity expansion and profitability changes", "recommendation": "Hold with Caution"}, "nvda": {"company": "NVIDIA", "title": "NVIDIA AI Wave Analysis", "description": "Explosive growth in AI chip demand, strong data center business, occupying a core position in the artificial intelligence revolution", "recommendation": "Strong Buy"}}, "metrics": {"technical": "Technical Score", "fundamental": "Fundamental Score", "sentiment": "Market Sentiment", "positive": "Positive", "neutral": "Mixed", "veryPositive": "Very Optimistic"}, "stats": {"cases": "Analysis Cases", "accuracy": "Prediction Accuracy", "avgTime": "Average Analysis Time", "dimensions": "Analysis Dimensions"}, "buttons": {"viewAnalysis": "View Full Analysis"}}, "resources": {"title": "Learning Resource Center", "subtitle": "From beginner to expert, enhance your investment analysis skills in every aspect.", "cards": {"quickStart": {"title": "5-Minute Quick Start", "description": "Even beginners can quickly get started and create your first stock analysis task", "features": {"register": "Account registration and setup", "create": "Create analysis tasks", "interpret": "Interpret analysis results"}, "button": "Start Learning"}, "education": {"title": "Investment Knowledge Academy", "description": "Systematically learn stock investment knowledge, from basic concepts to advanced strategies", "features": {"theory": "Basic investment theory", "technical": "Technical analysis methods", "risk": "Risk management strategies"}, "button": "Enter Academy"}, "guide": {"title": "Practical Operation Guide", "description": "Real case analysis, master practical application skills of AI analysis results", "features": {"interpretation": "Analysis report interpretation", "timing": "Investment timing", "portfolio": "Portfolio allocation strategies"}, "button": "View Guide"}, "faq": {"title": "Frequently Asked Questions", "description": "Quickly find answers to your concerns and resolve usage doubts", "features": {"usage": "Feature usage questions", "analysis": "Analysis result questions", "account": "Account-related questions"}, "button": "View FAQ"}, "consultation": {"title": "Expert Online Consultation", "description": "One-on-one professional guidance, senior investment advisors answer your questions", "features": {"realtime": "Real-time online consultation", "personalized": "Personalized advice", "strategy": "Investment strategy development"}, "button": "Consult Now"}, "community": {"title": "Investor Community", "description": "Exchange insights with like-minded investors and share successful experiences", "features": {"sharing": "Investment experience sharing", "discussion": "Market opinion discussions", "events": "Expert live events"}, "button": "Join Community"}}}, "ui": {"scrollHint": "Scroll down to explore more", "keyboardHint": "or use ↓ key", "expertDiscussion": {"title": "Expert Discussion Records", "description": "Complete preservation of analyst discussion processes, making investment logic clear and traceable", "features": {"record": "• Complete expert opinion records", "logic": "• Clear presentation of analysis logic", "history": "• Convenient discussion history queries"}}, "dataManagement": {"title": "Historical Data Management", "description": "Comprehensive data management system, making every analysis traceable", "features": {"archive": "• Complete analysis task archiving", "query": "• Convenient historical record queries", "support": "• Investment decision data support"}}, "langGraph": {"title": "LangGraph Architecture", "description": "Built with LangGraph, ensuring flexibility and modular design", "features": {"models": "• Support for o1-preview and gpt-4o models", "flexible": "• Flexible configuration and extension capabilities", "modular": "• Modular AI agent architecture"}}}}, "footer": {"title": "TradingAgents", "description": "Professional AI-driven stock analysis platform", "company": {"description": "Professional AI-driven stock analysis platform providing comprehensive intelligent support for investment decisions. Through multi-agent collaboration, we deliver in-depth market analysis and investment recommendations."}, "sections": {"features": {"title": "Core Features", "createTask": "Create Analysis Task", "taskManagement": "Task Management", "messageView": "Message View", "realTimeMonitoring": "Real-time Monitoring", "investmentAnalysis": "Investment Analysis"}, "resources": {"title": "Learning Resources", "quickStart": "Quick Start", "investmentAcademy": "Investment Academy", "practicalGuide": "Practical Guide", "faq": "FAQ", "education": "Investment Education"}, "support": {"title": "Help & Support", "userGuide": "User Guide", "expertConsultation": "Expert Consultation", "community": "Investor Community", "contactSupport": "Contact Support", "feedback": "<PERSON><PERSON><PERSON>"}}, "legal": {"privacy": "Privacy Policy", "terms": "Terms of Service", "disclaimer": "Disclaimer"}, "quickLinks": "Quick Links", "features": "Features", "about": "About Us", "contact": "Contact Us", "privacy": "Privacy Policy", "terms": "Terms of Service", "copyright": "© 2024 TradingAgents. Professional AI-driven stock analysis platform."}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import"}}