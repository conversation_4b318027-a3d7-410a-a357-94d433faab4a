'use client';

import {
  MailOutlined,
  PhoneOutlined,
  SaveOutlined,
  UsergroupDeleteOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Form,
  Input,
  message,
  Row,
  Select,
  Switch,
  Upload,
} from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import { useState } from 'react';

const { Option } = Select;
const { TextArea } = Input;

// 定义用户偏好设置的类型
interface UserPreferences {
  emailNotification: boolean;
  pushNotification: boolean;
  riskLevel: string;
  preferredIndustry: string[];
}

export default function ProfilePage() {
  const [form] = Form.useForm();
  const [preferences, setPreferences] = useState<UserPreferences>({
    emailNotification: true,
    pushNotification: false,
    riskLevel: 'medium',
    preferredIndustry: ['tech', 'finance'],
  });

  // 保存设置
  const handleSave = () => {
    message.success('个人设置已保存');
  };

  // 处理头像上传
  const handleAvatarUpload = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功');
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6 p-6 bg-slate-50 dark:bg-slate-900 min-h-screen">
      <Card title="个人资料" className="shadow-sm">
        <div className="flex items-center space-x-6 mb-6">
          <Upload
            name="avatar"
            showUploadList={false}
            action="/api/upload"
            onChange={handleAvatarUpload}
          >
            <Avatar
              size={80}
              icon={<UserOutlined />}
              className="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </Upload>
          <div>
            <h3 className="text-lg font-semibold">上传头像</h3>
            <p className="text-gray-500">点击头像更换照片</p>
          </div>
        </div>

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            name: '张明',
            email: '<EMAIL>',
            phone: '138****8888',
            company: '某科技公司',
            position: 'IT工程师',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="姓名" name="name">
                <Input prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="邮箱" name="email">
                <Input prefix={<MailOutlined />} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="手机号" name="phone">
                <Input prefix={<PhoneOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="所在公司" name="company">
                <Input prefix={<UsergroupDeleteOutlined />} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="职位" name="position">
                <Input prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="投资经验" name="experience">
                <Select defaultValue="3-5年">
                  <Option value="0-1年">0-1年</Option>
                  <Option value="1-3年">1-3年</Option>
                  <Option value="3-5年">3-5年</Option>
                  <Option value="5-10年">5-10年</Option>
                  <Option value="10年以上">10年以上</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="个人简介" name="bio">
            <TextArea rows={4} placeholder="简单介绍一下您的投资理念..." />
          </Form.Item>
        </Form>
      </Card>

      <Card title="偏好设置" className="shadow-sm">
        <div className="space-y-6">
          <div>
            <h4 className="font-semibold mb-4">通知设置</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>邮件通知</span>
                <Switch
                  checked={preferences.emailNotification}
                  onChange={(checked: boolean) =>
                    setPreferences({ ...preferences, emailNotification: checked })
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <span>推送通知</span>
                <Switch
                  checked={preferences.pushNotification}
                  onChange={(checked: boolean) =>
                    setPreferences({ ...preferences, pushNotification: checked })
                  }
                />
              </div>
            </div>
          </div>

          <Divider />

          <div>
            <h4 className="font-semibold mb-4">投资偏好</h4>
            <div className="space-y-4">
              <Form.Item label="风险偏好">
                <Select
                  value={preferences.riskLevel}
                  onChange={(val: string) => setPreferences({ ...preferences, riskLevel: val })}
                  style={{ width: 200 }}
                >
                  <Option value="conservative">保守型</Option>
                  <Option value="medium">稳健型</Option>
                  <Option value="aggressive">激进型</Option>
                </Select>
              </Form.Item>

              <Form.Item label="关注行业">
                <Select
                  mode="multiple"
                  value={preferences.preferredIndustry}
                  onChange={(val: string[]) =>
                    setPreferences({ ...preferences, preferredIndustry: val })
                  }
                  style={{ width: '100%' }}
                >
                  <Option value="tech">科技</Option>
                  <Option value="finance">金融</Option>
                  <Option value="consumption">消费</Option>
                  <Option value="healthcare">医疗</Option>
                  <Option value="energy">能源</Option>
                  <Option value="real-estate">房地产</Option>
                </Select>
              </Form.Item>
            </div>
          </div>

          <Divider />

          <div>
            <h4 className="font-semibold mb-4">隐私设置</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>公开个人资料</span>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <span>允许其他用户查看我的关注列表</span>
                <Switch />
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="text-center">
        <Button type="primary" size="large" icon={<SaveOutlined />} onClick={handleSave}>
          保存设置
        </Button>
      </div>
    </div>
  );
}
