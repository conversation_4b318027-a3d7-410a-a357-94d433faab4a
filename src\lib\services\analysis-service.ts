import { api } from '../api';
import { ApiResponse } from '../api';
import { Workflow } from '@/types/database';

// 分析配置接口
export interface AnalysisConfig {
  ticker: string;
  analysisDate: string;
  selectedAnalysts: string[];
  llmProvider: string;
  deepThinkLlm: string;
  quickThinkLlm: string;
  maxDebateRounds: number;
  maxRiskDiscussRounds: number;
  onlineTools: boolean;
  researchDepth: string;
}

// 分析状态接口
export interface AnalysisState {
  currentStage: string;
  progress: number;
  isComplete: boolean;
  error?: string;
}

// 代理状态接口
export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  lastUpdate: string;
  message?: string;
}

// 分析报告接口
export interface AnalysisReport {
  type: string;
  title: string;
  content: string;
  timestamp: string;
  agent: string;
}

// 交易决策接口
export interface TradingDecision {
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  reasoning: string;
  riskLevel: 'low' | 'medium' | 'high';
  targetPrice?: number;
  stopLoss?: number;
  positionSize?: number;
  timeHorizon?: string;
  timestamp: string;
}

// 分析相关 API 方法
export const analysisApi = {
  // 获取分析状态
  getAnalysisStatus: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/analysis/${workflowId}/status`);
  },

  // 停止分析
  stopAnalysis: (workflowId: string): Promise<ApiResponse<void>> => {
    return api.post(`/api/analysis/${workflowId}/stop`);
  },

  // 获取代理状态
  getAgentStatuses: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/analysis/${workflowId}/agents`);
  },

  // 获取分析报告
  getReports: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/analysis/${workflowId}/reports`);
  },

  // 获取交易决策
  getTradingDecision: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/analysis/${workflowId}/decision`);
  },

  // 开始分析
  startAnalysis: (taskId: string): Promise<ApiResponse<any>> => {
    return api.post('/api/analysis/start', { taskId });
  },

  // 获取分析历史
  getAnalysisHistory: (params: {
    ticker?: string;
    limit?: number;
    page?: number;
    status?: string[];
  }): Promise<ApiResponse<{ workflows: Workflow[]; pagination: any }>> => {
    return api.get('/api/analysis/history', { params });
  },

  // 获取分析统计
  getAnalysisStats: (): Promise<ApiResponse<any>> => {
    return api.get('/api/analysis/stats');
  },

  // 比较分析
  compareAnalyses: (workflowIds: string[]): Promise<ApiResponse<any>> => {
    return api.post('/api/analysis/compare', { workflowIds });
  },

  // 获取最近分析
  getRecentAnalyses: (limit?: number): Promise<ApiResponse<any>> => {
    return api.get('/api/analysis/recent', { params: { limit } });
  },

  // 获取综合报告
  getComprehensiveReport: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/analysis/${workflowId}/comprehensive-report`);
  },

  // 导出报告
  exportReport: async (workflowId: string, format: 'pdf' | 'markdown' | 'json'): Promise<Blob> => {
    const response = await fetch(`/api/analysis/${workflowId}/export?format=${format}`, {
      method: 'GET',
      credentials: 'include', // 确保包含 cookies
    });

    if (!response.ok) {
      throw new Error(`导出失败: ${response.status}`);
    }

    return response.blob();
  },

  // 重启分析
  restartAnalysis: (workflowId: string): Promise<ApiResponse<any>> => {
    return api.post(`/api/analysis/${workflowId}/restart`);
  },
};

// API 方法 (保持向后兼容)
export const tradingApi = {
  // 开始分析
  startAnalysis: (config: AnalysisConfig): Promise<ApiResponse<{ analysisId: string }>> => {
    return api.post('/api/langgraph/analysis/start', config);
  },

  // 获取分析状态
  getAnalysisStatus: (analysisId: string): Promise<ApiResponse<AnalysisState>> => {
    return analysisApi.getAnalysisStatus(analysisId);
  },

  // 获取代理状态
  getAgentStatuses: (analysisId: string): Promise<ApiResponse<AgentStatus[]>> => {
    return analysisApi.getAgentStatuses(analysisId);
  },

  // 获取分析报告
  getReports: (analysisId: string): Promise<ApiResponse<AnalysisReport[]>> => {
    return analysisApi.getReports(analysisId);
  },

  // 获取交易决策
  getTradingDecision: (analysisId: string): Promise<ApiResponse<TradingDecision | null>> => {
    return analysisApi.getTradingDecision(analysisId);
  },

  // 停止分析
  stopAnalysis: (analysisId: string): Promise<ApiResponse<void>> => {
    return analysisApi.stopAnalysis(analysisId);
  },

  // 获取股票实时数据
  getStockData: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/stock/${ticker}`, {
      params: { date },
    });
  },

  // 获取新闻数据
  getNewsData: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/news/${ticker}`, {
      params: { date },
    });
  },

  // 获取技术指标数据
  getTechnicalIndicators: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/technical/${ticker}`, {
      params: { date },
    });
  },

  // 获取基本面数据
  getFundamentalsData: (ticker: string, date: string): Promise<ApiResponse<any>> => {
    return api.get(`/api/data/fundamentals/${ticker}`, {
      params: { date },
    });
  },

  // 健康检查
  healthCheck: (): Promise<ApiResponse<{ status: string; version: string }>> => {
    return api.get('/api/health');
  },

  // 获取分析历史
  getAnalysisHistory: (params: {
    ticker?: string;
    limit?: number;
    page?: number;
  }): Promise<ApiResponse<{ workflows: Workflow[]; pagination: any }>> => {
    return api.get('/api/analysis/history', { params });
  },
};