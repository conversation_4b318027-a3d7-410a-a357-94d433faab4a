'use client';

import { AnalysisComparisonCharts } from '@/components/analysis/AnalysisComparisonCharts';
import { AnalysisComparisonSelector } from '@/components/analysis/AnalysisComparisonSelector';
import { AnalysisComparisonTable } from '@/components/analysis/AnalysisComparisonTable';
import { Breadcrumb } from '@/components/navigation/Breadcrumb';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { usePageTitle } from '@/hooks/usePageTitle';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

interface ComparisonData {
  comparisons: any[];
  summary: {
    totalAnalyses: number;
    tickers: string[];
    statuses: Record<string, number>;
    avgDuration: number;
    successRate: number;
  };
}

function AnalysisComparePageContent() {
  const [selectedWorkflowIds, setSelectedWorkflowIds] = useState<string[]>([]);
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'charts'>('table');

  const searchParams = useSearchParams();
  const router = useRouter();

  // Set page title and description
  usePageTitle('分析对比', '对比多个分析结果，发现差异和趋势');

  // 从URL参数初始化选中的工作流
  useEffect(() => {
    const idsParam = searchParams?.get('ids');
    if (idsParam) {
      const ids = idsParam.split(',').filter(Boolean);
      if (ids.length >= 2) {
        setSelectedWorkflowIds(ids);
        loadComparison(ids);
      }
    }
  }, []);

  // 加载对比数据
  const loadComparison = async (workflowIds: string[]) => {
    if (workflowIds.length < 2) {
      setError('请至少选择两个分析进行对比');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/analysis/compare', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workflowIds }),
      });

      if (!response.ok) {
        throw new Error('获取对比数据失败');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '获取对比数据失败');
      }

      setComparisonData(result.data);

      // 更新URL参数
      const params = new URLSearchParams();
      params.set('ids', workflowIds.join(','));
      router.push(`/analysis/compare?${params}`, { scroll: false });
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 处理选择变化
  const handleSelectionChange = (workflowIds: string[]) => {
    setSelectedWorkflowIds(workflowIds);
    if (workflowIds.length >= 2) {
      loadComparison(workflowIds);
    } else {
      setComparisonData(null);
      setError(null);
    }
  };

  // 导出对比结果
  const handleExport = async () => {
    if (!comparisonData) return;

    try {
      // 创建导出数据
      const exportData = {
        exportTime: new Date().toISOString(),
        summary: comparisonData.summary,
        comparisons: comparisonData.comparisons.map((c) => ({
          workflowId: c.workflow.workflow_id,
          ticker: c.workflow.ticker,
          title: c.workflow.title,
          status: c.workflow.status,
          createdAt: c.workflow.created_at,
          completedAt: c.workflow.completed_at,
          duration: c.metrics.duration,
          totalReports: c.metrics.totalReports,
          errorCount: c.metrics.errorCount,
          finalDecision: c.finalDecision
            ? {
                decisionType: c.finalDecision.decision_type,
                confidenceLevel: c.finalDecision.confidence_level,
                rationale: c.finalDecision.decision_rationale,
              }
            : null,
        })),
      };

      // 创建并下载文件
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analysis-comparison-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('导出失败:', err);
    }
  };

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 面包屑导航 */}
        <div className="mb-4">
          <Breadcrumb />
        </div>

        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-slate-100">分析对比</h1>
          <p className="mt-2 text-gray-600">选择多个分析进行详细对比，发现差异和趋势</p>
        </div>

        {/* 选择器 */}
        <div className="mb-6">
          <AnalysisComparisonSelector
            selectedIds={selectedWorkflowIds}
            onSelectionChange={handleSelectionChange}
            maxSelection={5}
          />
        </div>

        {/* 错误提示 */}
        {error && (
          <Card className="p-4 mb-6 bg-red-50 border-red-200">
            <div className="flex items-center">
              <div className="text-red-500 text-xl mr-3">⚠️</div>
              <div>
                <h3 className="text-red-800 font-medium">对比失败</h3>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          </Card>
        )}

        {/* 加载状态 */}
        {loading && (
          <Card className="p-8">
            <div className="flex items-center justify-center">
              <LoadingSpinner size="lg" />
              <span className="ml-3 text-gray-600">正在加载对比数据...</span>
            </div>
          </Card>
        )}

        {/* 对比结果 */}
        {comparisonData && !loading && (
          <>
            {/* 操作栏 */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  正在对比 {comparisonData.summary.totalAnalyses} 个分析
                </span>
                <div className="flex items-center space-x-2">
                  {comparisonData.summary.tickers.map((ticker) => (
                    <span
                      key={ticker}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {ticker}
                    </span>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'table' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  表格视图
                </Button>
                <Button
                  variant={viewMode === 'charts' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('charts')}
                >
                  图表视图
                </Button>
                <Button variant="secondary" size="sm" onClick={handleExport}>
                  导出结果
                </Button>
              </div>
            </div>

            {/* 对比内容 */}
            {viewMode === 'table' ? (
              <AnalysisComparisonTable data={comparisonData} />
            ) : (
              <AnalysisComparisonCharts data={comparisonData} />
            )}
          </>
        )}

        {/* 空状态 */}
        {!comparisonData && !loading && !error && (
          <Card className="p-12">
            <div className="text-center">
              <div className="text-gray-400 text-6xl mb-4 dark:text-gray-500">📊</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2 dark:text-slate-100">开始分析对比</h3>
              <p className="text-gray-600 mb-4 dark:text-gray-400">
                请在上方选择至少两个分析进行对比，系统将为您展示详细的对比结果。
              </p>
              <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                <p>• 支持同时对比最多5个分析</p>
                <p>• 可以对比不同股票的分析结果</p>
                <p>• 提供表格和图表两种视图模式</p>
                <p>• 支持导出对比结果</p>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
export default function AnalysisComparePage() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <AnalysisComparePageContent />
    </Suspense>
  );
}
