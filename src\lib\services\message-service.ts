import { api } from '../api';
import { ApiResponse } from '../api';

// 消息数据类型定义
export interface Message {
  id: number;
  message_id: string;
  task_id: string;
  message_type: 'human' | 'ai' | 'system' | 'tool';
  content: string;
  metadata: any;
  sequence_number: number;
  parent_message_id: string | null;
  created_at: string;
}

// 分页信息
export interface Pagination {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// API响应类型
export interface MessagesResponse {
  messages: Message[];
  pagination: Pagination;
}

// 消息相关API方法
export const messageApi = {
  // 获取消息列表
  getMessages: (params: {
    taskId?: string;
    conversationId?: string;
    limit?: number;
    offset?: number;
    messageType?: string;
  }): Promise<ApiResponse<MessagesResponse>> => {
    return api.get('/api/database/messages', { params });
  },
};