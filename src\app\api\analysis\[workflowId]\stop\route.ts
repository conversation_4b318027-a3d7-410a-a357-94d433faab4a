import { analysisService } from '@/lib/analysis-service';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(
    request: NextRequest,
    { params }: { params: Promise<{ workflowId: string }> }
) {
    try {
        const { workflowId } = await params;

        if (!workflowId) {
            return NextResponse.json(
                {
                    success: false,
                    error: '工作流ID不能为空',
                    timestamp: new Date().toISOString(),
                },
                { status: 400 }
            );
        }

        // Stop the analysis
        await analysisService.stopAnalysis(workflowId);

        return NextResponse.json({
            success: true,
            message: '分析已停止',
            timestamp: new Date().toISOString(),
        });
    } catch (error: any) {
        console.error('Stop analysis error:', error);

        // Handle specific error types
        if (error.code === 'ANALYSIS_NOT_FOUND') {
            return NextResponse.json(
                {
                    success: false,
                    error: '分析不存在或未运行',
                    timestamp: new Date().toISOString(),
                },
                { status: 404 }
            );
        }

        return NextResponse.json(
            {
                success: false,
                error: error.message || '停止分析失败',
                timestamp: new Date().toISOString(),
            },
            { status: 500 }
        );
    }
}