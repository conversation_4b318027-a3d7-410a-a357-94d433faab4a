import {
  analysisPeriodOptions as apiAnalysisPeriodOptions,
  researchDepthOptions as apiResearchDepthOptions,
  AnalysisPeriod,
  ResearchDepth,
  TaskStatus,
} from '@/types/database';

// 分析师选项
export const analystOptions = [
  { value: 'fundamental', label: '基本面分析师', description: '财务数据和价值评估' },
  { value: 'technical', label: '技术分析师', description: '价格走势和交易信号' },
  { value: 'sentiment', label: '情绪分析师', description: '市场情绪和投资者行为' },
  { value: 'news', label: '新闻分析师', description: '新闻事件和市场影响' },
  { value: 'risk', label: '风险管理师', description: '风险评估和控制' },
];

// 分析类型选项
export const analysisTypeOptions = [
  { value: 'comprehensive', label: '综合分析', description: '全方位深度分析' },
  { value: 'quick', label: '快速分析', description: '重点关注核心指标' },
  { value: 'custom', label: '自定义分析', description: '根据需求定制分析' },
];

// 时间周期选项
export const timePeriodOptions = apiAnalysisPeriodOptions.map((option) => ({
  value: option,
  label:
    option === '1d'
      ? '1天'
      : option === '1w'
      ? '1周'
      : option === '1m'
      ? '1个月'
      : option === '3m'
      ? '3个月'
      : option === '6m'
      ? '6个月'
      : option === '1y'
      ? '1年'
      : '自定义',
  description: option === 'custom' ? '自定义时间范围' : `分析过去${option}的数据`,
}));

// 研究深度选项
export const researchDepthOptions = apiResearchDepthOptions.map((option) => ({
  value: option,
  label: option === 'shallow' ? '浅度分析' : option === 'medium' ? '中度分析' : '深度分析',
  description:
    option === 'shallow' ? '快速概览' : option === 'medium' ? '标准分析' : '全面深入分析',
}));

// 任务状态选项
export const taskStatusOptions: { value: TaskStatus; label: string }[] = [
  { value: 'pending', label: '待处理' },
  { value: 'running', label: '运行中' },
  { value: 'completed', label: '已完成' },
  { value: 'failed', label: '失败' },
];

// 通用回显函数
export const getOptionLabel = (
  options: { value: string; label: string }[],
  value: string
): string => {
  return options.find((option) => option.value === value)?.label || value;
};

// 专用回显函数
export const getAnalystLabel = (value: string) => getOptionLabel(analystOptions, value);
export const getAnalysisTypeLabel = (value: string) => getOptionLabel(analysisTypeOptions, value);
export const getTimePeriodLabel = (value: AnalysisPeriod) =>
  getOptionLabel(timePeriodOptions, value);
export const getResearchDepthLabel = (value: ResearchDepth) =>
  getOptionLabel(researchDepthOptions, value);
export const getTaskStatusLabel = (value: TaskStatus) => getOptionLabel(taskStatusOptions, value);
