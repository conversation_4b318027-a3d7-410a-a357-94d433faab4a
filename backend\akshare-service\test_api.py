#!/usr/bin/env python3
"""
AKShare 后端服务测试脚本

这个脚本用于测试 AKShare 后端服务的各个 API 接口是否正常工作。
可以在服务启动后运行此脚本来验证功能。

使用方法:
    python test_api.py

注意: 确保后端服务已经启动在 http://localhost:5000
"""

import requests
import json
import time
from typing import Dict, Any

# 配置
BASE_URL = "http://localhost:5000"
TIMEOUT = 30  # 请求超时时间（秒）


def print_section(title: str):
    """打印测试章节标题"""
    print(f"\n{'='*50}")
    print(f" {title}")
    print(f"{'='*50}")


def print_result(test_name: str, success: bool, data: Any = None, error: str = None):
    """打印测试结果"""
    status = "✅ 成功" if success else "❌ 失败"
    print(f"{status} {test_name}")
    
    if success and data:
        if isinstance(data, dict):
            print(f"   数据条数: {data.get('count', 'N/A')}")
            if 'data' in data and isinstance(data['data'], list) and len(data['data']) > 0:
                print(f"   示例数据: {json.dumps(data['data'][0], ensure_ascii=False, indent=2)[:200]}...")
        else:
            print(f"   响应: {str(data)[:200]}...")
    
    if error:
        print(f"   错误: {error}")
    print()


def test_health_check():
    """测试健康检查接口"""
    print_section("健康检查测试")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
        
        if response.status_code == 200:
            data = response.json()
            print_result("健康检查", True, data)
            return True
        else:
            print_result("健康检查", False, error=f"HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print_result("健康检查", False, error=str(e))
        return False


def test_root_endpoint():
    """测试根路径接口"""
    print_section("根路径测试")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=TIMEOUT)
        
        if response.status_code == 200:
            data = response.json()
            print_result("根路径", True, data)
            return True
        else:
            print_result("根路径", False, error=f"HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print_result("根路径", False, error=str(e))
        return False


def test_stock_history():
    """测试股票历史数据接口"""
    print_section("股票历史数据测试")
    
    # 测试数据：平安银行 (000001)
    test_data = {
        "symbol": "000001",
        "period": "daily",
        "start_date": "20241201",  # 最近一个月的数据
        "end_date": "20241231"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/stock/history",
            json=test_data,
            timeout=TIMEOUT
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print_result("股票历史数据", True, data)
                return True
            else:
                print_result("股票历史数据", False, error=data.get('error', '未知错误'))
                return False
        else:
            print_result("股票历史数据", False, error=f"HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print_result("股票历史数据", False, error=str(e))
        return False


def test_stock_news():
    """测试股票新闻接口"""
    print_section("股票新闻测试")
    
    test_data = {
        "symbol": "000001",
        "limit": 5
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/stock/news",
            json=test_data,
            timeout=TIMEOUT
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print_result("股票新闻", True, data)
                return True
            else:
                print_result("股票新闻", False, error=data.get('error', '未知错误'))
                return False
        else:
            print_result("股票新闻", False, error=f"HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print_result("股票新闻", False, error=str(e))
        return False


def test_stock_fundamental():
    """测试股票基本面数据接口"""
    print_section("股票基本面数据测试")
    
    test_data = {
        "symbol": "000001",
        "indicator": "all"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/stock/fundamental",
            json=test_data,
            timeout=TIMEOUT
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print_result("股票基本面数据", True, data)
                return True
            else:
                print_result("股票基本面数据", False, error=data.get('error', '未知错误'))
                return False
        else:
            print_result("股票基本面数据", False, error=f"HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print_result("股票基本面数据", False, error=str(e))
        return False


def test_stock_realtime():
    """测试股票实时数据接口"""
    print_section("股票实时数据测试")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/stock/realtime?symbol=000001",
            timeout=TIMEOUT
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print_result("股票实时数据", True, data)
                return True
            else:
                print_result("股票实时数据", False, error=data.get('error', '未知错误'))
                return False
        else:
            print_result("股票实时数据", False, error=f"HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print_result("股票实时数据", False, error=str(e))
        return False


def test_market_overview():
    """测试市场概览接口"""
    print_section("市场概览测试")
    
    try:
        response = requests.get(f"{BASE_URL}/api/market/overview", timeout=TIMEOUT)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print_result("市场概览", True, data)
                return True
            else:
                print_result("市场概览", False, error=data.get('error', '未知错误'))
                return False
        else:
            print_result("市场概览", False, error=f"HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print_result("市场概览", False, error=str(e))
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 AKShare 后端服务")
    print(f"📍 服务地址: {BASE_URL}")
    print(f"⏱️  超时时间: {TIMEOUT}秒")
    
    # 等待服务启动
    print("\n⏳ 等待服务启动...")
    time.sleep(2)
    
    # 执行测试
    tests = [
        ("基础功能", [test_health_check, test_root_endpoint]),
        ("股票数据", [test_stock_history, test_stock_realtime, test_stock_fundamental]),
        ("新闻和市场", [test_stock_news, test_market_overview])
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for category, test_functions in tests:
        print(f"\n🧪 测试类别: {category}")
        
        for test_func in test_functions:
            total_tests += 1
            if test_func():
                passed_tests += 1
    
    # 输出测试总结
    print_section("测试总结")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests / total_tests * 100):.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！AKShare 后端服务运行正常。")
        return 0
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查服务状态。")
        return 1


if __name__ == "__main__":
    exit(main())