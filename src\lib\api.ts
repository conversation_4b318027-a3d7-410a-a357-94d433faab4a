import axios from 'axios';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

// 创建 axios 实例
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // 重要：允许发送和接收 cookies
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从 userStore 获取认证头
    if (typeof window !== 'undefined') {
      // 动态导入 userStore 以避免 SSR 问题
      import('@/store/userStore').then(({ default: useUserStore }) => {
        const authHeaders = useUserStore.getState().getAuthHeaders();
        Object.assign(config.headers, authHeaders);
      });

      // 备用方案：直接从 localStorage 获取
      const sessionId = localStorage.getItem('sessionId');
      if (sessionId) {
        config.headers['X-Session-ID'] = sessionId;
      }
    }
    config.withCredentials = true;

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 统一API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 如果响应数据已经是 ApiResponse 格式，直接返回
    if (response.data && typeof response.data.success === 'boolean') {
      return response.data;
    }
    // 否则，包装成 ApiResponse 格式
    return {
      success: true,
      data: response.data,
      message: 'Request successful',
    };
  },
  (error) => {
    console.error('API Error:', error);

    const response: ApiResponse = {
      success: false,
      data: null,
      message: error.response?.data?.message || error.message || 'An unknown error occurred',
      code: error.response?.status,
    };
    return Promise.resolve(response); // 返回一个 resolved promise，让业务代码处理
  }
);