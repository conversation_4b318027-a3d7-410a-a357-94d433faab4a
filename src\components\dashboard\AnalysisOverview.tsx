'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon,
  SignalSlashIcon,
  StopIcon,
  WifiIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { motion } from 'framer-motion';

// Types for the component
interface AnalysisOverviewProps {
  status: {
    workflowId: string;
    taskId?: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    currentStage: string;
    ticker: string;
    title: string;
    description?: string;
    createdAt: string;
    startedAt?: string;
    completedAt?: string;
    error?: string;
  };
  isConnected: boolean;
  isConnecting?: boolean;
  onStopAnalysis?: () => void;
  isStoppingAnalysis?: boolean;
}

/**
 * AnalysisOverview Component
 *
 * Displays comprehensive overview of analysis workflow including:
 * - Basic workflow information and status
 * - Real-time connection status indicator
 * - Status badges with appropriate colors
 * - Time information display
 *
 * Requirements: 需求 2.1, 需求 2.2, 需求 8.1
 */
export function AnalysisOverview({
  status,
  isConnected,
  isConnecting = false,
  onStopAnalysis,
  isStoppingAnalysis = false,
}: AnalysisOverviewProps) {
  // Helper functions for status display
  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case 'completed':
        return 'success';
      case 'running':
        return 'secondary';
      case 'failed':
      case 'cancelled':
        return 'destructive';
      case 'pending':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (statusValue: string) => {
    switch (statusValue) {
      case 'completed':
        return '已完成';
      case 'running':
        return '进行中';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      case 'pending':
        return '等待中';
      default:
        return statusValue;
    }
  };

  const getStatusIcon = (statusValue: string) => {
    switch (statusValue) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'running':
        return <ClockIcon className="h-5 w-5 text-yellow-500 animate-pulse" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'cancelled':
        return <ExclamationCircleIcon className="h-5 w-5 text-gray-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-slate-800 shadow-lg rounded-lg p-6 mb-6"
    >
      {/* Header Section */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            {getStatusIcon(status.status)}
            <h1 className="text-2xl font-bold text-slate-900 dark:text-white">{status.title}</h1>
          </div>

          <div className="flex items-center space-x-4 text-sm text-slate-500 mb-2">
            <span className="font-medium">
              股票代码:
              <code className="ml-1 bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded text-blue-600 dark:text-blue-400">
                {status.ticker}
              </code>
            </span>
            <span>工作流ID: {status.workflowId}</span>
            {status.taskId && <span>任务ID: {status.taskId}</span>}
          </div>

          {/* Description */}
          {status.description && (
            <p className="text-slate-600 dark:text-slate-400 text-sm">{status.description}</p>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {/* Connection Status Indicator */}
          <div className="flex items-center space-x-2">
            {isConnecting ? (
              <>
                <div className="w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm text-yellow-600 dark:text-yellow-400">连接中</span>
              </>
            ) : isConnected ? (
              <>
                <WifiIcon className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600 dark:text-green-400">实时连接</span>
              </>
            ) : (
              <>
                <SignalSlashIcon className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-600 dark:text-red-400">连接断开</span>
              </>
            )}
          </div>

          {/* Status Badge */}
          <Badge variant={getStatusColor(status.status)}>{getStatusText(status.status)}</Badge>

          {/* Stop Analysis Button */}
          {status.status === 'running' && onStopAnalysis && (
            <Button
              onClick={onStopAnalysis}
              disabled={isStoppingAnalysis}
              variant="danger"
              size="sm"
              className="flex items-center space-x-2"
            >
              <StopIcon className="h-4 w-4" />
              <span>{isStoppingAnalysis ? '停止中...' : '停止分析'}</span>
            </Button>
          )}
        </div>
      </div>

      {/* Progress Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-slate-700 dark:text-slate-300">分析进度</span>
          <span className="text-sm text-slate-600 dark:text-slate-400">{status.progress}%</span>
        </div>

        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 mb-2">
          <motion.div
            className={`h-2 rounded-full transition-all duration-500 ${
              status.status === 'completed'
                ? 'bg-green-500'
                : status.status === 'failed'
                ? 'bg-red-500'
                : status.status === 'cancelled'
                ? 'bg-gray-500'
                : 'bg-gradient-to-r from-blue-500 to-purple-500'
            }`}
            initial={{ width: 0 }}
            animate={{ width: `${status.progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        <p className="text-sm text-slate-600 dark:text-slate-400">
          当前阶段: <span className="font-medium">{status.currentStage}</span>
        </p>
      </div>

      {/* Time Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="bg-slate-50 dark:bg-slate-700 p-3 rounded-lg">
          <dt className="text-slate-600 dark:text-slate-400 font-medium">创建时间</dt>
          <dd className="text-slate-900 dark:text-white mt-1">
            {format(new Date(status.createdAt), 'yyyy-MM-dd HH:mm:ss')}
          </dd>
        </div>

        {status.startedAt && (
          <div className="bg-slate-50 dark:bg-slate-700 p-3 rounded-lg">
            <dt className="text-slate-600 dark:text-slate-400 font-medium">开始时间</dt>
            <dd className="text-slate-900 dark:text-white mt-1">
              {format(new Date(status.startedAt), 'yyyy-MM-dd HH:mm:ss')}
            </dd>
          </div>
        )}

        {status.completedAt && (
          <div className="bg-slate-50 dark:bg-slate-700 p-3 rounded-lg">
            <dt className="text-slate-600 dark:text-slate-400 font-medium">完成时间</dt>
            <dd className="text-slate-900 dark:text-white mt-1">
              {format(new Date(status.completedAt), 'yyyy-MM-dd HH:mm:ss')}
            </dd>
          </div>
        )}

        {status.startedAt && status.completedAt && (
          <div className="bg-slate-50 dark:bg-slate-700 p-3 rounded-lg">
            <dt className="text-slate-600 dark:text-slate-400 font-medium">总耗时</dt>
            <dd className="text-slate-900 dark:text-white mt-1">
              {Math.round(
                (new Date(status.completedAt).getTime() - new Date(status.startedAt).getTime()) /
                  1000
              )}{' '}
              秒
            </dd>
          </div>
        )}
      </div>

      {/* Error Message */}
      {status.error && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <div className="flex items-start space-x-2">
            <XCircleIcon className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-800 dark:text-red-200">错误信息</h4>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">{status.error}</p>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

export default AnalysisOverview;
