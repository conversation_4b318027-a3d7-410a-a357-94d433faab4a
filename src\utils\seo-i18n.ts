import { Metadata } from 'next';

interface LocaleMetadata {
  [key: string]: {
    title: string;
    description: string;
    keywords: string[];
  };
}

const localeMetadata: LocaleMetadata = {
  zh: {
    title: 'TradingAgents - AI智能投资决策平台 | 多智能体股票分析',
    description:
      'TradingAgents利用先进的多智能体AI技术，为您提供深度股票分析和智能投资建议。整合市场、新闻、基本面等多维度数据，助您做出更明智的金融决策。',
    keywords: [
      '股票分析',
      'AI投资',
      '智能投资',
      '股票推荐',
      '投资决策',
      '多智能体',
      '大语言模型',
      'LangGraph',
      '金融科技',
      'FinTech',
      '市场分析',
      '技术分析',
      '基本面分析',
      '新闻分析',
      '情绪分析',
      '风险管理',
      '投资策略',
      '股票预测',
      '量化投资',
      '智能投顾',
      'TradingAgents',
      '交易智能体',
      '金融AI',
      '投资AI',
      '股票AI',
    ],
  },
  en: {
    title: 'TradingAgents: AI Platform for Smart Stock Analysis & Investment',
    description:
      'Make smarter investment decisions with TradingAgents. Our AI-powered platform uses multi-agent technology for in-depth stock analysis and actionable insights.',
    keywords: [
      'stock analysis',
      'AI investment',
      'smart investing',
      'stock recommendations',
      'investment decisions',
      'multi-agent',
      'large language models',
      'LangGraph',
      'fintech',
      'financial technology',
      'market analysis',
      'technical analysis',
      'fundamental analysis',
      'news analysis',
      'sentiment analysis',
      'risk management',
      'investment strategy',
      'stock prediction',
      'quantitative investing',
      'robo advisor',
      'TradingAgents',
      'trading agents',
      'financial AI',
      'investment AI',
      'stock AI',
      'automated trading',
      'algorithmic trading',
      'portfolio management',
      'financial analytics',
    ],
  },
};

export function generateMetadataForLocale(locale: string): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagent.top';
  const isEnglish = locale === 'en';
  const metadata = localeMetadata[locale] || localeMetadata.zh;

  return {
    title: {
      default: metadata.title,
      template: `%s | ${
        isEnglish
          ? 'TradingAgents - AI Stock Analysis Platform'
          : 'TradingAgents - AI智能股票分析平台'
      }`,
    },
    description: metadata.description,
    keywords: metadata.keywords,
    authors: [
      { name: 'TradingAgents Team', url: `${baseUrl}${isEnglish ? '/en' : ''}` },
      { name: 'Tauric Research' },
    ],
    creator: 'TradingAgents Team',
    publisher: 'TradingAgents',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
    },
    alternates: {
      canonical: `${baseUrl}${isEnglish ? '/en' : ''}`,
      languages: {
        'zh-CN': baseUrl,
        'en-US': `${baseUrl}/en`,
      },
    },
    openGraph: {
      type: 'website',
      locale: isEnglish ? 'en_US' : 'zh_CN',
      url: `${baseUrl}${isEnglish ? '/en' : ''}`,
      title: metadata.title,
      description: metadata.description,
      siteName: 'TradingAgents',
      images: [
        {
          url: '/tradingAgentLogoWithBg.png',
          width: 1200,
          height: 630,
          alt: isEnglish
            ? 'TradingAgents - AI-Powered Stock Analysis Platform'
            : 'TradingAgents - AI智能股票分析平台',
          type: 'image/png',
        },
        {
          url: '/tradingAgentLogo.png',
          width: 512,
          height: 512,
          alt: 'TradingAgents Logo',
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
      site: '@TradingAgents',
      creator: '@TradingAgents',
      images: ['/tradingAgentLogoWithBg.png'],
    },
    icons: {
      icon: [
        { url: '/tradingAgent.ico' },
        { url: '/tradingAgentLogo.ico', sizes: '16x16', type: 'image/x-icon' },
      ],
      shortcut: '/tradingAgent.ico',
      apple: [
        { url: '/tradingAgentLogo.png' },
        { url: '/tradingAgentLogo.png', sizes: '180x180', type: 'image/png' },
      ],
      other: [
        {
          rel: 'apple-touch-icon-precomposed',
          url: '/tradingAgentLogo.png',
        },
      ],
    },
    manifest: '/manifest.json',
  };
}

// 生成页面特定的元数据
export function generatePageMetadataForLocale(
  locale: string,
  pageKey: string,
  customTitle?: string,
  customDescription?: string
): Metadata {
  const baseMetadata = generateMetadataForLocale(locale);
  const isEnglish = locale === 'en';

  const pageTitle = customTitle || (isEnglish ? 'Page' : '页面');
  const pageDescription =
    customDescription ||
    (typeof baseMetadata.description === 'string' ? baseMetadata.description : '');

  return {
    ...baseMetadata,
    title: pageTitle,
    description: pageDescription,
    openGraph: {
      ...baseMetadata.openGraph,
      title: pageTitle,
      description: pageDescription,
    },
    twitter: {
      ...baseMetadata.twitter,
      title: pageTitle,
      description: pageDescription,
    },
  };
}
