#!/usr/bin/env python3
"""
简单的服务测试脚本
"""

import requests
import json

def test_service():
    """测试服务基本功能"""
    base_url = "http://localhost:5000"
    
    print("测试 AKShare 服务基本功能")
    print("=" * 40)
    
    # 1. 测试根路径
    print("1. 测试根路径 (/)")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print()
    
    # 2. 测试健康检查
    print("2. 测试健康检查 (/health)")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print()
    
    # 3. 测试API文档
    print("3. 测试API文档 (/docs)")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ API文档可访问")
        else:
            print(f"   错误: {response.status_code}")
    except Exception as e:
        print(f"   异常: {e}")

if __name__ == "__main__":
    test_service()