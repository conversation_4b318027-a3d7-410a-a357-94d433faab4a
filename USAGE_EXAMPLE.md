# 任务视图切换功能使用示例

## 基本使用

### 1. 访问任务管理页面

```
http://localhost:3000/tasks
```

### 2. 视图切换操作

在页面顶部，你会看到一个视图切换控件：

```
[🔲 卡片] [📊 表格]
```

- 点击"卡片"按钮切换到网格卡片视图
- 点击"表格"按钮切换到紧凑表格视图

### 3. 视图特点对比

#### 卡片视图 (Grid View)
- ✅ 详细的任务信息展示
- ✅ 直观的进度条和状态指示
- ✅ 适合详细查看单个任务
- ✅ 响应式网格布局

#### 表格视图 (Table View)
- ✅ 紧凑的信息展示
- ✅ 一目了然的任务列表
- ✅ 适合快速浏览多个任务
- ✅ 支持水平滚动

## 功能演示

### 切换到表格视图

1. 在任务页面点击"表格"按钮
2. 页面会切换到表格布局
3. 所有任务以表格行的形式展示
4. 每行包含：股票代码、标题、状态、进度等信息

### 切换到卡片视图

1. 在表格视图中点击"卡片"按钮
2. 页面会切换到网格卡片布局
3. 每个任务以卡片形式展示
4. 卡片包含详细的任务信息和操作按钮

### 偏好设置持久化

- 用户选择的视图会自动保存
- 下次访问页面时会恢复上次的选择
- 无需重新设置

## 代码示例

### 在其他页面中使用 ViewToggle 组件

```tsx
import { ViewToggle, ViewType } from '@/components/tasks/ViewToggle';
import { useState } from 'react';

function MyComponent() {
  const [currentView, setCurrentView] = useState<ViewType>('grid');

  const handleViewChange = (view: ViewType) => {
    setCurrentView(view);
    // 可以添加其他逻辑，如保存到本地存储
    localStorage.setItem('my-view-preference', view);
  };

  return (
    <div>
      <ViewToggle 
        currentView={currentView} 
        onViewChange={handleViewChange} 
      />
      
      {currentView === 'grid' ? (
        <div className="grid grid-cols-3 gap-4">
          {/* 网格内容 */}
        </div>
      ) : (
        <table className="w-full">
          {/* 表格内容 */}
        </table>
      )}
    </div>
  );
}
```

### 自定义样式

```tsx
<ViewToggle 
  currentView={currentView} 
  onViewChange={handleViewChange}
  className="my-custom-class"
/>
```

## 键盘快捷键 (未来功能)

计划添加的键盘快捷键：
- `Ctrl + 1`: 切换到卡片视图
- `Ctrl + 2`: 切换到表格视图

## 移动端体验

- 在移动设备上，视图切换按钮会自动适配屏幕尺寸
- 表格视图支持水平滚动
- 卡片视图在移动端会变为单列布局

## 性能优化

- 视图切换不会重新加载数据
- 使用 React 的条件渲染优化性能
- 状态管理避免不必要的重渲染